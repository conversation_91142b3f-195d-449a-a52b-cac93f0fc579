<script setup lang="ts">
import { computed } from 'vue';
import VueApexCharts from 'vue3-apexcharts';
import type { ApexOptions } from 'apexcharts';
import { isLightModeEnabled } from '@/composables/theme';

interface GridData {
  producedEnergy: {
    value: number;
    unit: string;
  } | undefined;
  energyFromNetwork: {
    value: number;
    unit: string;
  } | undefined;
  energyToNetwork: {
    value: number;
    unit: string;
  } | undefined;
  houseConsumption: {
    value: number;
    unit: string;
  } | undefined;
}

interface Props {
  gridData: GridData;
}

const props = defineProps<Props>();

// Prvý graf: Rozdelenie výroby (do siete vs domáca spotreba)
const productionDistributionData = computed(() => {
  if (!props.gridData.producedEnergy || !props.gridData.energyToNetwork || !props.gridData.houseConsumption) {
    return [];
  }

  const produced = props.gridData.producedEnergy.value;
  const toNetwork = props.gridData.energyToNetwork.value;
  const homeConsumptionFromPV = Math.max(0, produced - toNetwork);

  return [
    homeConsumptionFromPV, // Domáca spotreba z FV
    toNetwork // Do siete
  ];
});

const productionDistributionLabels = computed(() => [
  'Domáca spotreba z FV',
  'Predané do siete'
]);

// Druhý graf: Pokrytie domácej spotreby (z FV vs zo siete)
const consumptionCoverageData = computed(() => {
  if (!props.gridData.houseConsumption || !props.gridData.energyFromNetwork || !props.gridData.producedEnergy || !props.gridData.energyToNetwork) {
    return [];
  }

  const totalConsumption = props.gridData.houseConsumption.value;
  const fromNetwork = props.gridData.energyFromNetwork.value;
  const fromPV = Math.max(0, totalConsumption - fromNetwork);

  return [
    fromPV, // Z FV
    fromNetwork // Zo siete
  ];
});

const consumptionCoverageLabels = computed(() => [
  'Pokryté z FV',
  'Zo siete'
]);

// Spoločné nastavenia pre grafy
const baseChartOptions: ApexOptions = {
  chart: {
    type: 'donut',
    background: 'transparent',
  },
  theme: {
    mode: isLightModeEnabled.value ? 'light' : 'dark',
  },
  colors: ['#f1c40f', '#27ae60'], // Žltá pre FV, zelená pre sieť
  legend: {
    position: 'bottom',
    fontSize: '12px',
    labels: {
      colors: isLightModeEnabled.value ? '#000' : '#fff',
    },
  },
  plotOptions: {
    pie: {
      donut: {
        size: '60%',
        labels: {
          show: true,
          name: {
            show: true,
            fontSize: '14px',
            color: isLightModeEnabled.value ? '#000' : '#fff',
          },
          value: {
            show: true,
            fontSize: '16px',
            fontWeight: 'bold',
            color: isLightModeEnabled.value ? '#000' : '#fff',
            formatter: (val: string) => {
              const unit = props.gridData.producedEnergy?.unit || 'kWh';
              return `${Math.round(parseFloat(val) * 1000) / 1000} ${unit}`;
            },
          },
          total: {
            show: true,
            showAlways: true,
            fontSize: '14px',
            color: isLightModeEnabled.value ? '#000' : '#fff',
            formatter: () => {
              const unit = props.gridData.producedEnergy?.unit || 'kWh';
              return unit;
            },
          },
        },
      },
    },
  },
  dataLabels: {
    enabled: false,
  },
  tooltip: {
    enabled: true,
    y: {
      formatter: (val: number) => {
        const unit = props.gridData.producedEnergy?.unit || 'kWh';
        return `${Math.round(val * 1000) / 1000} ${unit}`;
      },
    },
  },
};

// Špecifické nastavenia pre prvý graf
const productionChartOptions = computed<ApexOptions>(() => ({
  ...baseChartOptions,
  labels: productionDistributionLabels.value,
  colors: ['#f39c12', '#27ae60'], // Oranžová pre domácu spotrebu, zelená pre sieť
  title: {
    text: 'Rozdelenie výroby',
    align: 'center',
    style: {
      fontSize: '14px',
      fontWeight: 'bold',
      color: isLightModeEnabled.value ? '#000' : '#fff',
    },
  },
}));

// Špecifické nastavenia pre druhý graf
const consumptionChartOptions = computed<ApexOptions>(() => ({
  ...baseChartOptions,
  labels: consumptionCoverageLabels.value,
  colors: ['#f1c40f', '#e74c3c'], // Žltá pre FV, červená pre sieť
  title: {
    text: 'Pokrytie spotreby',
    align: 'center',
    style: {
      fontSize: '14px',
      fontWeight: 'bold',
      color: isLightModeEnabled.value ? '#000' : '#fff',
    },
  },
}));

// Kontrola, či máme dostatok dát na zobrazenie grafov
const hasValidData = computed(() => {
  return props.gridData.producedEnergy &&
         props.gridData.energyFromNetwork &&
         props.gridData.energyToNetwork &&
         props.gridData.houseConsumption;
});
</script>

<template>
  <div class="flex flex-col p-1 h-full">
    <div v-if="hasValidData" class="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
      <!-- Prvý graf: Rozdelenie výroby -->
      <div class="flex flex-col items-center justify-center">
        <VueApexCharts
          type="donut"
          width="100%"
          height="300"
          :options="productionChartOptions"
          :series="productionDistributionData"
        />
      </div>

      <!-- Druhý graf: Pokrytie spotreby -->
      <div class="flex flex-col items-center justify-center">
        <VueApexCharts
          type="donut"
          width="100%"
          height="300"
          :options="consumptionChartOptions"
          :series="consumptionCoverageData"
        />
      </div>
    </div>

    <!-- Fallback pre prípad nedostupných dát -->
    <div v-else class="flex flex-col items-center justify-center h-48 text-gray-500">
      <div class="text-center">
        <div class="text-lg font-medium mb-2">
          Nedostupné dáta
        </div>
        <div class="text-sm">
          Pre zobrazenie grafov sú potrebné všetky energetické údaje
        </div>
      </div>
    </div>
  </div>
</template>
