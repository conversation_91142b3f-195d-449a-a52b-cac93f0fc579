<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import SolarSchemeWithExports from '@/SolarSchemeWithExports.vue';
import { isLightModeEnabled } from '@/composables/theme';
import { animateArrows, drawLine, updateTextPosition, type ExtendedTextOptions } from '@/util/facades/svg-scheme.ts';
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';
import { FlowChartSection, getFlowChartSectionValue, getInstallationInverterType, normalizeUnitByThreshold } from '../helpers/inverter-helper';
import type { InstallationDetailData } from '../types/installation-types';
import type { WaterHeaterCurrentDataTransformed } from '@/util/types/api-responses-heater';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
  waterHeaterCurrentData: WaterHeaterCurrentDataTransformed,
  installationDetail: InstallationDetailData,
}

type DirectionalRecord = {
  in?: { value: SVGElement | undefined };
  out?: { value: SVGElement | undefined };
};

interface ArrowsDirection { entityDirection: 'in' | 'out', order: 'ascending' | 'descending' }

const props = defineProps<Props>();

const schemeRef = ref<InstanceType<typeof SolarSchemeWithExports> | null>(null);
const inverterType = computed(() => getInstallationInverterType({ installationDetail: props.installationDetail }));

let disabledGroups: (keyof (InstanceType<typeof SolarSchemeWithExports>['wholeGroups']))[] = [
  'heatingGroup',
  'wallBoxGroup'
];

const updateTextSafe = (
  circleElement: SVGCircleElement,
  valueResult: { value?: number; unit?: string } | null,
  key: string,
  options?: ExtendedTextOptions
) => {
  if (valueResult?.value !== undefined && valueResult?.value !== null) {
    const value = Math.abs(valueResult.value);
    const formatted = options?.fractionDigits !== undefined
      ? value.toFixed(options.fractionDigits)
      : value.toString();

    updateTextPosition(circleElement, `${formatted}${valueResult.unit ?? ''}`, key, options);
    return;
  }
  updateTextPosition(circleElement, '-', key, options);
};

const animateArrowSafe = (valueResult: { value?: number; unit?: string } | null, svgGroupLocation: DirectionalRecord, direction: ArrowsDirection) => {
  if (svgGroupLocation.in?.value) {
    animateArrows(svgGroupLocation['in'].value!, direction.order, false);
  }
  if (svgGroupLocation.out?.value) {
    animateArrows(svgGroupLocation['out'].value!, direction.order, false);
  }
  animateArrows(svgGroupLocation[direction.entityDirection]!.value!, direction.order, false);
  if (valueResult && (valueResult?.value !== undefined || valueResult?.value !== null) && valueResult?.value !== 0) {
    animateArrows(svgGroupLocation[direction.entityDirection]!.value!, direction.order, true);
    return;
  }
  animateArrows(svgGroupLocation[direction.entityDirection]!.value!, direction.order, false);
};

const getArrowsDirection = (directionToEntity: 'ascending' | 'descending', valueResult: { value?: number; unit?: string } | null,): {entityDirection: 'in' | 'out', order: 'ascending' | 'descending'} => {
  if ((valueResult?.value !== undefined || valueResult?.value !== null) && valueResult!.value! > 0) {
    return directionToEntity === 'ascending' ? { order: 'ascending', entityDirection: 'out' } : { order: 'descending', entityDirection: 'out' };
  }
  return directionToEntity === 'ascending' ? { order:'descending', entityDirection: 'in' } : { order:'ascending', entityDirection: 'in' };
};

const render = () => {
  if (!props.inverterCurrentData?.metrics || Object.keys(props.inverterCurrentData.metrics).length === 0 || !inverterType.value) {
    return;
  }

  const mergedCurrentData: InverterCurrentDataTransformed | WaterHeaterCurrentDataTransformed = {
    metrics: {
      ...props.inverterCurrentData.metrics,
      ...props.waterHeaterCurrentData?.metrics
    },
    time: props.inverterCurrentData.time,
  };

  const photovoltaics = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.PHOTOVOLTAICS, currentData: props.inverterCurrentData });
  const battery = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.BATTERY, currentData: props.inverterCurrentData });
  const batterySoc = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.BATTERY_SOC, currentData: props.inverterCurrentData });
  const grid = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.GRID, currentData: props.inverterCurrentData });
  const gridSell = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.GRID_SELL, currentData: props.inverterCurrentData, valueMultiplier: 0.001 });
  const gridBuy = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.GRID_BUY, currentData: props.inverterCurrentData, valueMultiplier: 0.001 });
  const household = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.HOUSEHOLD, currentData: mergedCurrentData, min: 0 });
  const waterHeater = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.HEATING, currentData: props.waterHeaterCurrentData });
  const waterHeaterTemp = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.HEATING_TEMP, currentData: props.waterHeaterCurrentData, min: 0 });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.main.value!, normalizeUnitByThreshold({ ...photovoltaics!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'photovoltaics_main', { fontSize: '4.5' });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPower.value!, '-', 'photovoltaics_currentPower', { classList: ['main-chart-text'] });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPowerPercent.value!, '- %', 'photovoltaics_currentPowerPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  if (props.installationDetail?.pv_capacity) {
    updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.system.value!, `${props.installationDetail?.pv_capacity} kW`, 'photovoltaics_system', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 1.6, offsetY: -0.04 });
  } else {
    updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.system.value!, '-', 'photovoltaics_system', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 1.9, offsetY: -0.08 });
  }

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.todayEnergy.value!, '-', 'photovoltaics_todayEnergy', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.todayEnergyPercent.value!, '- %', 'photovoltaics_todayEnergyPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.todayEnergyAverage.value!, '- W', 'photovoltaics_todayEnergyAverage', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 0.5, offsetY: -0.14 });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.battery.batteryMain.value!, normalizeUnitByThreshold({ ...battery!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'battery_batteryMain', { fontSize: '4.5' });
  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.battery.batteryState.value!, batterySoc, 'battery_batteryState', { classList: ['main-chart-text'] });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.battery.batteryMain.value!, percentage.value.toString() + 'W', 'battery_batteryMain', { fontSize: '4.5' });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.battery.batteryState.value!, percentage.value.toString(), 'battery_batteryState', { classList: ['main-chart-text'] });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.grid.main.value!, normalizeUnitByThreshold({ ...grid!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'grid_main', { fontSize: '4.5' });
  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.grid.consumption.value!, { ...gridBuy, unit: undefined }, 'grid_consumption', { classList: ['main-chart-text'] });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.main.value!, percentage.value.toString() + 'W', 'grid_main', { fontSize: '4.5' });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.consumption.value!, percentage.value.toString(), 'grid_consumption', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.consumptionPercent.value!, '- %', 'grid_consumptionPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.consumptionTotal.value!, '- W', 'grid_consumptionTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.75, offsetY: -0.14 });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.grid.currentPower.value!, { ...gridSell, unit: undefined }, 'grid_currentPower', { classList: ['main-chart-text'] });
  // updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.currentPower.value!, percentage.value.toString(), 'grid_currentPower', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.currentPowerPercent.value!, '- %', 'grid_currentPowerPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.currentPowerTotal.value!, '- W', 'grid_currentPowerTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.75, offsetY: -0.14 });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.today.value!, '0', 'heating_today', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.todayPercent.value!, '0%', 'heating_todayPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.todayTotal.value!, '0W', 'heating_todayTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.75, offsetY: -0.14 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.main.value!, '0W', 'heating_main', { fontSize: '4.5' });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.tuv.main.value!, normalizeUnitByThreshold({ ...waterHeater!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'tuv_main', { fontSize: '4.5' });
  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.tuv.tuvStateCurrentTemperatureCenter.value!, waterHeaterTemp, 'tuv_tuvStatePercent', { classList: ['main-chart-text'] });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.household.main.value!, normalizeUnitByThreshold({ ...household!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'household_main', { fontSize: '4.5' });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.today.value!, '-', 'household_today', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.todayPercent.value!, '- %', 'household_todayPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.todayTotal.value!, '- W', 'household_todayTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.5, offsetY: -0.14 });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.today.value!, '0', 'wallBox_today', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.todayPercent.value!, '0%', 'wallBox_todayPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.todayTotal.value!, '0W', 'wallBox_todayTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.5, offsetY: -0.14 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.main.value!, '0W', 'wallBox_main', { fontSize: '4.5' });

  if (props.installationDetail?.pv_capacity) {
    const photovoltaicsPercent = ((photovoltaics?.value ?? 0) / (props.installationDetail?.pv_capacity ?? 1) / 1000) * 100;
    updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPower.value!, { value: photovoltaicsPercent, unit: '%' }, 'photovoltaics_currentPowerPercent', { fractionDigits: 1, classList: ['main-chart-text'] });
    drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsCurrentPowerChart.value!, 100 - photovoltaicsPercent);
  } else {
    updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPower.value!, null, 'photovoltaics_currentPowerPercent', { classList: ['main-chart-text'] });
    drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsCurrentPowerChart.value!, 100);
  }
  // drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsTodaysPowerChart.value!, 100 - percentage.value);
  if (!disabledGroups.includes('batteryGroup') && batterySoc?.value) {
    drawLine(schemeRef.value!.charts.battery.batteryStateChart.value!, 100 - batterySoc?.value);
  } else {
    drawLine(schemeRef.value!.charts.battery.batteryStateChart.value!, 100);
  }
  if (!disabledGroups.includes('tuvGroup') && props.waterHeaterCurrentData?.metrics?.temp_max?.value && waterHeaterTemp?.value) {
    drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100 - (Math.round(waterHeaterTemp!.value) / props.waterHeaterCurrentData.metrics.temp_max.value) * 100);
  } else {
    drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100);
  }
  // drawLine(schemeRef.value!.charts.grid.gridConsumptionChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.grid.gridPowerChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.heating.heatingTodayConsumptionChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.household.householdConsumptionChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.wallBox.wallboxConsumptionChart.value!, 100 - percentage.value);

  const batteryArrowsDirection = getArrowsDirection('ascending', battery);
  const gridArrowsDirection = getArrowsDirection('ascending', grid);
  animateArrowSafe(photovoltaics, schemeRef.value!.arrows.photovoltaics, { entityDirection: 'out', order: 'ascending' });
  animateArrowSafe(battery, schemeRef.value!.arrows.battery, batteryArrowsDirection);
  animateArrowSafe(grid, schemeRef.value!.arrows.grid, gridArrowsDirection);
  animateArrowSafe(household, schemeRef.value!.arrows.household, { entityDirection: 'in', order: 'ascending' });
  animateArrowSafe(waterHeater, schemeRef.value!.arrows.tuv, { entityDirection: 'in', order: 'ascending' });
  // animateArrows(schemeRef.value!.arrows.photovoltaics.out.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.battery.in.value!, 'descending', true);
  // animateArrows(schemeRef.value!.arrows.grid.out.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.heating.in.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.tuv.in.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.household.in.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.wallBox.in.value!, 'ascending', true);
};

if (props.inverterCurrentData?.metrics && (!props.inverterCurrentData.metrics.atk_battery_charge?.value && !props.inverterCurrentData.metrics.atk_battery_power?.value)) {
  disabledGroups.push('batteryGroup');
}

if (!props.waterHeaterCurrentData || (!props.waterHeaterCurrentData?.metrics?.temp?.value)) {
  disabledGroups.push('tuvGroup');
}

const updateDisabledGroups = () => {
  Object.keys(schemeRef.value!.wholeGroups).forEach(g => {
    schemeRef.value?.wholeGroups[g as keyof typeof schemeRef.value.wholeGroups]?.value?.classList.remove('opacity-30');
  });
  disabledGroups.forEach(g => schemeRef.value!.wholeGroups[g].value!.classList.add('opacity-30'));
};

onMounted(() => {
  if (props.inverterCurrentData) {
    updateDisabledGroups();
    render();
  }
});

watch([() => props.inverterCurrentData, () => props.waterHeaterCurrentData], () => {
  if (props.inverterCurrentData?.metrics && (!props.inverterCurrentData.metrics.atk_battery_charge?.value && !props.inverterCurrentData.metrics.atk_battery_power?.value)) {
    disabledGroups.push('batteryGroup');
  } else {
    disabledGroups = disabledGroups.filter(g => g !== 'batteryGroup');
  }
  if (!props.waterHeaterCurrentData || (!props.waterHeaterCurrentData?.metrics?.temp?.value)) {
    disabledGroups.push('tuvGroup');
  } else {
    disabledGroups = disabledGroups.filter(g => g !== 'tuvGroup');
  }
  updateDisabledGroups();
  render();
});

</script>

<template>
  <div
    class="w-full h-full rounded-2xl"
    :class="[
      isLightModeEnabled ? 'bg-white' : 'bg-prim-col-foreground-1',
      !inverterCurrentData?.metrics || Object.keys(inverterCurrentData.metrics).length === 0 ? 'opacity-30' : '',
    ]"
  >
    <SolarSchemeWithExports ref="schemeRef" />
  </div>
</template>
