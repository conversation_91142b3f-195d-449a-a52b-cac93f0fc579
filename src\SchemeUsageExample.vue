<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import Test3Scheme from '@/SolarSchemeWithExports.vue';
import { isLightModeEnabled } from './composables/theme';
import { animateArrows, drawLine, updateTextPosition } from '@/util/facades/svg-scheme.ts';

const percentage = ref(50); // Set initial percentage value (0 to 100)
const schemeRef = ref<InstanceType<typeof Test3Scheme>>();

const disabledGroups: (keyof (InstanceType<typeof Test3Scheme>['wholeGroups']))[] = [
  'heatingGroup',
  'tuvGroup',
  'wallBoxGroup'
];

const onChangeMode = () => {
  isLightModeEnabled.value = !isLightModeEnabled.value;
};

const render = () => {
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPower.value!, percentage.value.toString(), 'photovoltaics_currentPower', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPowerPercent.value!, percentage.value.toString() + '%', 'photovoltaics_currentPowerPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.system.value!, '10kW', 'photovoltaics_system', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 1.9, offsetY: -0.08 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.main.value!, percentage.value.toString() + 'W', 'photovoltaics_main', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.todayEnergy.value!, percentage.value.toString(), 'photovoltaics_todayEnergy', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.todayEnergyPercent.value!, percentage.value.toString() + '%', 'photovoltaics_todayEnergyPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.todayEnergyAverage.value!, '5W', 'photovoltaics_todayEnergyAverage', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 0.5, offsetY: -0.14 });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.battery.batteryState.value!, percentage.value.toString(), 'battery_batteryState', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.battery.batteryMain.value!, percentage.value.toString() + 'W', 'battery_batteryMain', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.consumption.value!, percentage.value.toString(), 'grid_consumption', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.consumptionPercent.value!, percentage.value.toString() + '%', 'grid_consumptionPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.consumptionTotal.value!, '5W', 'grid_consumptionTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.75, offsetY: -0.14 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.main.value!, percentage.value.toString() + 'W', 'grid_main', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.currentPower.value!, percentage.value.toString(), 'grid_currentPower', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.currentPowerPercent.value!, percentage.value.toString() + '%', 'grid_currentPowerPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.grid.currentPowerTotal.value!, '5W', 'grid_currentPowerTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.75, offsetY: -0.14 });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.today.value!, '0', 'heating_today', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.todayPercent.value!, '0%', 'heating_todayPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.todayTotal.value!, '0W', 'heating_todayTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.75, offsetY: -0.14 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.main.value!, '0W', 'heating_main', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.tuv.tuvState.value!, '0', 'tuv_tuvState', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.tuv.main.value!, '0W', 'tuv_main', { fontSize: '4.5' });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.tuv.tuvStatePercent.value!, '0%', 'tuv_tuvStatePercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.today.value!, percentage.value.toString(), 'household_today', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.todayPercent.value!, percentage.value.toString() + '%', 'household_todayPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.todayTotal.value!, '5W', 'household_todayTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.5, offsetY: -0.14 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.household.main.value!, percentage.value.toString() + 'W', 'household_main', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.today.value!, '0', 'wallBox_today', { classList: ['main-chart-text'] });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.todayPercent.value!, '0%', 'wallBox_todayPercent', { fontSize: '2.5', fontWeight: '400', color: '#8d8d8d', offsetX: 0.5 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.todayTotal.value!, '0W', 'wallBox_todayTotal', { fontSize: '1.7', fontWeight: '500', color: '#8d8d8d', offsetX: 2.5, offsetY: -0.14 });
  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.main.value!, '0W', 'wallBox_main', { fontSize: '4.5' });

  drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsCurrentPowerChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsTodaysPowerChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.battery.batteryStateChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.grid.gridConsumptionChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.grid.gridPowerChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.heating.heatingTodayConsumptionChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.household.householdConsumptionChart.value!, 100 - percentage.value);
  drawLine(schemeRef.value!.charts.wallBox.wallboxConsumptionChart.value!, 100 - percentage.value);
};

onMounted(() => {
  disabledGroups.forEach(g => schemeRef.value!.wholeGroups[g].value!.classList.add('opacity-30'));

  animateArrows(schemeRef.value!.arrows.photovoltaics.out.value!, 'ascending', true);
  animateArrows(schemeRef.value!.arrows.battery.in.value!, 'descending', true);
  animateArrows(schemeRef.value!.arrows.grid.out.value!, 'ascending', true);
  animateArrows(schemeRef.value!.arrows.heating.in.value!, 'ascending', true);
  animateArrows(schemeRef.value!.arrows.tuv.in.value!, 'ascending', true);
  animateArrows(schemeRef.value!.arrows.household.in.value!, 'ascending', true);
  animateArrows(schemeRef.value!.arrows.wallBox.in.value!, 'ascending', true);

  render();
});

// Watch for changes in percentage and update line drawing
watch(percentage, () => {
  render();
});

</script>

<template>
  <div class="w-screen h-screen flex items-center justify-center fixed left-0 top-0 z-10000" :class="isLightModeEnabled ? 'bg-white' : 'bg-prim-col-foreground-1'">
    <input
      v-model="percentage"
      type="range"
      min="0"
      max="100"
      class="fixed top-2 left-2 bg-red-500"
    >
    <button class="fixed top-2 left-40 bg-blue-800" @click="onChangeMode">
      Change theme
    </button>
    <Test3Scheme ref="schemeRef" />
  </div>
</template>
