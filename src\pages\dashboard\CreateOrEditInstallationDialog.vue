<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { Textarea as ShadCnTextarea } from '@/shadcn-components/ui/textarea';
import { useInstallationStore } from '@/stores/installation-store';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import type { InstallationClassification, InstallationDetailData } from '@/pages/installation/types/installation-types.ts';
import { CircleXIcon } from 'lucide-vue-next';
import { formDataFromObject } from '@/util/facades/forms';

interface Props {
  isEditDialog?: boolean,
  installationId?: string,
}

interface ExtendedInstallationData {
  title: string;
  description: string;
  sharedTo?: InstallationDetailData['sharedTo'];
  id?: string;
  pv_capacity: number | null;
  battery_capacity?: number | null;
  pv_count?: number | null;
  locality?: string | null;
  image?: File | null;
  classification?: InstallationClassification | null;
}

const props = withDefaults(defineProps<Props>(), {
  isEditDialog: false,
  installationId: undefined,
});

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const installationStore = useInstallationStore();
const installationData = reactive<ExtendedInstallationData>({
  title: '',
  description: '',
  sharedTo: [],
  id: undefined,
  pv_capacity: null,
  battery_capacity: null,
  pv_count: null,
  locality: '',
  image: null,
  classification: null,
});
const imageUrl = ref<string>();

const emit = defineEmits(['installationCreated', 'installationUpdated']);

const fetchInstallationDetail = async() => {
  try {
    const { data: responseData } = await customAxios.get<{data: InstallationDetailData}>(`/user/${props.installationId}/detail`);
    installationData.title = responseData.data.title;
    installationData.description = responseData.data.description ?? '';
    installationData.sharedTo = responseData.data.sharedTo ?? [];
    installationData.id = responseData.data.id;
    installationData.pv_capacity = responseData.data.pv_capacity;
    installationData.battery_capacity = responseData.data.battery_capacity;
    installationData.pv_count = responseData.data.pv_count;
    installationData.locality = responseData.data.locality;
    installationData.classification = responseData.data.classification;
    if (responseData.data.hasImage) {
      const imageResponse = await customAxios.get(`/user/installations/${responseData.data.id}/image`, {
        responseType: 'blob',
      });
      imageUrl.value = URL.createObjectURL(imageResponse.data);
    }
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.failed-to-get-data'),
      timeout: 6000,
    });
    isOpened.value = false;
  }
};

const onSubmitEdit = async() => {
  try {
    const data = formDataFromObject({
      ...installationData,
      sharedTo: installationData.sharedTo?.map(user => user.id),
    });
    await customAxios.put(`/user/installations/${installationData!.id!}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    installationData.title = '';
    installationData.description = '';
    installationData.sharedTo = [];
    installationData.id = undefined;
    isOpened.value = false;
    emit('installationUpdated');
    deployToast(ToastType.SUCCESS, {
      text: t('installation.form.installation-success-edit'),
      timeout: 6000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('installation.form.installation-fail-edit'),
      timeout: 6000,
    });
  }
};

const onSubmitCreate = async() => {
  try {
    const data = formDataFromObject(installationData);
    const { data: postResponse } = await customAxios.post('/user/installations', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    installationStore.appendInstallation(postResponse.data);
    installationData.title = '';
    installationData.description = '';
    installationData.sharedTo = [];
    installationData.id = undefined;
    isOpened.value = false;
    emit('installationCreated', postResponse.data);
    deployToast(ToastType.SUCCESS, {
      text: t('installation.form.installation-success-create'),
      timeout: 6000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('installation.form.installation-fail-create'),
      timeout: 6000,
    });
  }
};

const removeShared = (user: InstallationDetailData['sharedTo'][0]) => {
  installationData.sharedTo = installationData.sharedTo!.filter(u => u.id !== user.id);
};

const onSubmit = () => {
  if (props.isEditDialog) {
    onSubmitEdit();
    return;
  }
  onSubmitCreate();
};

if (props.isEditDialog) {
  await fetchInstallationDetail();
}
</script>

<template>
  <Teleport to="body">
    <v-dialog
      v-model="isOpened"
      width="fit-content"
      class=""
      :style="{margin: 'auto'}"
    >
      <form
        autocomplete="off"
        class="bg-prim-col-1 p-6 rounded-2xl w-[35rem] max-w-[90vw] max-h-[90vh] overflow-y-scroll"
        @submit.prevent="onSubmit"
      >
        <h3 class="font-bold text-xl mb-2">
          <span v-if="isEditDialog">
            {{ $t('installation.edit-installation') }}
          </span>
          <span v-else>
            {{ $t('installation.create-installation') }}
          </span>
        </h3>
        <p v-if="!isEditDialog" class="text-black/50 dark:text-white/60 text-sm">
          {{ $t('installation.create-installation-desc') }}
        </p>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label
              for="new-installation-type"
              class="text-right w-20 min-w-20"
            >
              {{ $t('misc.title') }}
            </Label>
            <Input
              id="new-installation-type"
              v-model="installationData.title"
              name="type"
              type="text"
              required
            />
          </div>
          <div class="flex items-center gap-4">
            <Label
              for="new-user-mail"
              class="text-right w-20 min-w-20"
            >
              {{ $t('misc.description') }}
            </Label>
            <ShadCnTextarea
              id="new-installation-description"
              v-model="installationData.description"
              name="description"
              class="h-fit"
              type="email"
              :rows="5"
              style="resize: none;"
            />
          </div>
          <div class="flex items-center gap-4">
            <Label for="pv_capacity" class="text-right w-20 min-w-20">
              <div>
                {{ t('installation.form.pv-capacity') }}
              </div>
              <div class="text-[10px] font-extralight mt-0.5">
                (kW)
              </div>
            </Label>
            <Input
              id="pv_capacity"
              v-model.number="installationData.pv_capacity as number"
              name="pv_capacity"
              type="number"
              min="0"
              step="any"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="battery_capacity" class="text-right w-20 min-w-20">
              <div>
                {{ t('installation.form.battery-capacity') }}
              </div>
              <div class="text-[10px] font-extralight mt-0.5">
                (kWh)
              </div>
            </Label>
            <Input
              id="battery_capacity"
              v-model.number="installationData.battery_capacity as number"
              name="battery_capacity"
              type="number"
              min="0"
              step="any"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="pv_count" class="text-right w-20 min-w-20">
              {{ t('installation.form.panels-count') }}
            </Label>
            <Input
              id="pv_count"
              v-model.number="installationData.pv_count as number"
              name="pv_count"
              type="number"
              min="0"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="locality" class="text-right w-20 min-w-20">
              {{ $t('misc.locality') }}
            </Label>
            <Input
              id="locality"
              v-model="installationData.locality as string"
              name="locality"
              type="text"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="image" class="text-right w-20 min-w-20">
              {{ $t('misc.image') }}
            </Label>
            <input
              id="image"
              name="image"
              type="file"
              accept="image/png, image/jpeg"
              class="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-secondary file:text-secondary-foreground hover:file:bg-secondary/80"
              @change="e => installationData.image = (e.target as HTMLInputElement).files?.[0] ?? null"
            >
            <div v-if="imageUrl" class="ml-20">
              <img
                :src="imageUrl"
                alt="Preview"
                class="mt-2 rounded-md max-w-full max-h-40 object-contain border"
              >
            </div>
          </div>

          <div class="flex items-center gap-4">
            <Label for="classification" class="text-right w-20 min-w-20">
              {{ $t('misc.type') }}
            </Label>
            <select
              id="classification"
              v-model="installationData.classification"
              name="classification"
              class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm outline-hidden"
            >
              <option value="" disabled>
                {{ $t('misc.select-option') }}
              </option>
              <option value="on_grid">
                On-grid
              </option>
              <option value="hybrid">
                Hybrid
              </option>
            </select>
          </div>
          <div v-if="isEditDialog" class="flex items-center gap-4 w-full">
            <Label
              for="new-user-mail"
              class="text-right w-20 min-w-20"
            >
              {{ $t('installation.sharing') }}
            </Label>
            <div v-if="installationData.sharedTo?.length" class="max-h-36 overflow-y-auto w-full flex flex-col gap-1.5 custom-scrollbar-1">
              <div
                v-for="(user, idx) in installationData.sharedTo"
                :key="idx"
                class="bg-prim-col-foreground-1 p-1 rounded-md flex items-center justify-between"
              >
                <div>
                  {{ user.name }}
                </div>
                <button
                  type="button"
                  class="w-7 h-7 rounded-full cursor-pointer group"
                  :title="$t('installation.remove-shared-user')"
                  @click="removeShared(user)"
                >
                  <CircleXIcon class="w-6 group-hover:text-red-500" />
                </button>
              </div>
            </div>
            <div v-else class="text-xs bg-background p-2 w-full rounded-md select-none">
              {{ $t('installation.is-not-shared') }}
            </div>
          </div>
        </div>
        <div class="flex items-center justify-end gap-2">
          <Button
            type="button"
            variant="default"
            class="bg-gray-400/80 hover:bg-gray-400"
            @click="isOpened = false"
          >
            <span>{{ $t('misc.cancel') }}</span>
          </Button>
          <Button type="submit">
            {{ $t('misc.save') }}
          </Button>
        </div>
      </form>
    </v-dialog>
  </Teleport>
</template>
