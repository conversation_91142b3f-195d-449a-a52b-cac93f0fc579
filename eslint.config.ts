/* eslint-disable import/no-extraneous-dependencies */
import oxlint from 'eslint-plugin-oxlint';
import pluginVue from 'eslint-plugin-vue';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';
import { flatConfigs as importPluginFlatConfigs } from 'eslint-plugin-import';
import js from '@eslint/js';
import globals from 'globals';

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2024,
        process: 'readonly',
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly',
      },
    },
  },

  {
    name: 'app/files-to-ignore',
    ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**'],
  },

  pluginVue.configs['flat/recommended'],
  vueTsConfigs.recommended,
  oxlint.configs['flat/recommended'],

  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2024,
        process: 'readonly',
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly',
      },
    },
    rules: {
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-process-env': 'off',
      'max-len': 'off',
      'arrow-parens': ['error', 'as-needed'],
      'no-param-reassign': [2, { props: false }],
      'vue/multi-word-component-names': 'off',
      'indent': ['error', 2, { 'SwitchCase': 1 }],
      'quotes': ['error', 'single', { 'avoidEscape': true }],
      'no-var': 'error',
      'prefer-const': 'error',
      // 'no-unused-vars': ['error', { 'vars': 'all', 'args': 'after-used', 'ignoreRestSiblings': true }],
      'prefer-arrow-callback': ['error', { 'allowNamedFunctions': false, 'allowUnboundThis': true }],
      'semi': ['error', 'always'],
      'no-multiple-empty-lines': ['error', { 'max': 1, 'maxEOF': 1 }],
      'eqeqeq': ['error', 'always'],
      'curly': ['error', 'all'],
      'brace-style': ['error', '1tbs', { 'allowSingleLine': true }],
      'space-before-function-paren': ['error', 'never'],
      'dot-location': ['error', 'property'],
      'no-confusing-arrow': ['error', { 'allowParens': true }],
      'no-useless-constructor': 'error',
      'lines-around-comment': ['error', { 'beforeBlockComment': true, 'allowBlockStart': true }],
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
  js.configs.recommended,
  importPluginFlatConfigs.recommended,

  (importPluginFlatConfigs as Record<'typescript', any>).typescript,
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      'import/no-extraneous-dependencies': [
        'error',
        {
          devDependencies: ['tailwind.config.js', '**/tests/**'],
        },
      ],
      'import/prefer-default-export': 'off',
      'import/no-unresolved': 'off',
      'import/extensions': 'off',
    },
  },
  {
    rules: {
      'vue/max-attributes-per-line': [
        'error',
        {
          singleline: 3,
          multiline: {
            max: 1,
          },
        },
      ],
      'vue/html-closing-bracket-newline': [
        'error',
        {
          singleline: 'never',
          multiline: 'always',
        },
      ],
      'no-trailing-spaces': 'error',
      'object-curly-spacing': ['error', 'always'],
      '@typescript-eslint/no-unused-vars': 'error',
      'no-unused-vars': 'off'
    },
    ignores: [
      '.DS_Store',
      'node_modules',
      '/dist',
      '/build',
      '.env',
      '.env.local',
      '.env.*.local',
      'npm-debug.log*',
      'yarn-debug.log*',
      'yarn-error.log*',
      'pnpm-debug.log*',
      '.idea',
      '.vscode',
      '*.suo',
      '*.ntvs*',
      '*.njsproj',
      '*.sln',
      '*.sw?',
      '*.map',
      'public/version.json',
      'tsconfig.tsbuildinfo',
      'cypress',
      'src/assets/js/**/*.min.js',
      'src/shadcn-utils'
    ]
  }
  // },
);
