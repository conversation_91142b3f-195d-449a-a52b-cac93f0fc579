<script lang="ts" setup>
import { PanelLeft, Settings } from 'lucide-vue-next';
import { type NavLink, navLinks } from '@/router/navbar';
import { Button } from '@/shadcn-components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/shadcn-components/ui/sheet';
import { useAuthStore } from '@/stores/auth-store';

const authStore = useAuthStore();
const userPermissions = authStore.user?.permissions ?? [];

const filterFunction = (link: NavLink) => !link.neededPermissions || link.neededPermissions.every(permission => userPermissions.includes(permission));

const topNavLinks = navLinks.top.filter(filterFunction);
const bottomNavLinks = navLinks.bottom.filter(filterFunction);
</script>

<template>
  <Sheet :modal="true">
    <SheetTrigger as-child>
      <Button
        size="icon"
        variant="outline"
        class="sm:hidden outline-hidden bg-prim-col-foreground-2/50 border-0"
      >
        <PanelLeft class="h-5 w-5 text-white" />
        <span class="sr-only">Toggle Menu</span>
      </Button>
    </SheetTrigger>
    <SheetContent
      side="left"
      class="flex flex-col h-full sm:max-w-xs py-6 px-2 bg-prim-col-2 border-0"
    >
      <nav class="grid gap-6 text-lg font-medium">
        <SheetTrigger
          v-for="navLink in topNavLinks"
          :key="navLink.name"
          as-child
        >
          <router-link
            active-class="text-prim-col-selected-1!"
            class="flex items-center gap-4 px-2.5 text-prim-col-foreground-contrast"
            :to="{ name: navLink.name }"
          >
            <component
              :is="navLink.icon"
              class="h-5 w-5"
            />
            {{ $t(navLink.i18nTitle) }}
          </router-link>
        </SheetTrigger>
      </nav>
      <nav class="mt-auto mb-0 grid gap-6 text-lg font-medium">
        <SheetTrigger
          v-for="navLink in bottomNavLinks"
          :key="navLink.name"
          as-child
        >
          <router-link
            active-class="text-prim-col-selected-1!"
            class="flex items-center gap-4 px-2.5 text-prim-col-foreground-contrast"
            :to="{ name: navLink.name }"
          >
            <component
              :is="navLink.icon"
              class="h-5 w-5"
            />
            {{ $t(navLink.i18nTitle) }}
          </router-link>
        </SheetTrigger>
        <SheetTrigger as-child>
          <router-link
            active-class="text-prim-col-selected-1!"
            class="flex items-center gap-4 px-2.5 text-prim-col-foreground-contrast"
            :to="{ path: '/settings' }"
          >
            <Settings class="h-5 w-5" />
            {{ $t('misc.settings') }}
          </router-link>
        </SheetTrigger>
      </nav>
    </SheetContent>
  </Sheet>
</template>
