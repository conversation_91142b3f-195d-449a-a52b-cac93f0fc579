<script setup lang="ts">
import type { DeviceLastMetricsApiResponse, DeviceStatsMetricsApiResponse } from '@/util/types/api-responses';
import { DateTime } from 'luxon';
import type { DeviceInstance } from '../installation/types/installation-types';
import DeviceDetailMetricChartGroup from './DeviceDetailMetricChartGroup.vue';
import { ChevronLeftIcon } from 'lucide-vue-next';
import { routeMap } from '@/router/routes';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';

interface Props {
  deviceLastMetricsData: DeviceLastMetricsApiResponse;
  deviceDetail: DeviceInstance;
  deviceStats: DeviceStatsMetricsApiResponse;
  installationId: string;
  deviceId: string;
}

defineProps<Props>();

const forbiddenMetrics = [
  'big_packet',
  'small_packet',
  'datapoint',
];
</script>

<template>
  <div class="mx-auto max-w-(--breakpoint-3xl) grid gap-4">
    <router-link
      :replace="true"
      :to="{name: routeMap.home.children.installation.name, params: {installationId: installationId}}"
      class="w-fit"
    >
      <ShadCnButton type="button" class="flex items-center cursor-pointer pl-1 pr-2 py-2! h-fit gap-1">
        <ChevronLeftIcon class="h-4 w-4" />
        <div>
          {{ $t('installation.back-to-installation') }}
        </div>
      </ShadCnButton>
    </router-link>
    <section class="bg-prim-col-foreground-1 rounded-xl">
      <div class="p-4">
        <h2 class="text-lg leading-6 font-medium mb-4">
          {{ $t('device-detail.basic-information') }}
        </h2>

        <dl class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2">
          <div>
            <dt class="text-xs font-medium text-prim-col-foreground-contrast mb-1">
              {{ $t('device-detail.identifier') }}
            </dt>
            <dd class="font-medium">
              {{ deviceDetail.id }}
            </dd>
          </div>
        </dl>
        <dl class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="flex flex-col">
            <dt class="text-xs font-medium text-prim-col-foreground-contrast mb-1">
              {{ $t('device-detail.device-type') }}
            </dt>
            <dd class="font-medium">
              {{ deviceDetail.device?.type?.name || '-' }}
            </dd>
          </div>

          <div class="flex flex-col">
            <dt class="text-xs font-medium text-prim-col-foreground-contrast mb-1">
              {{ $t('device-detail.vendor') }}
            </dt>
            <dd class="font-medium">
              {{ deviceDetail.device?.vendor?.name || '-' }}
            </dd>
          </div>

          <div class="flex flex-col">
            <dt class="text-xs font-medium text-prim-col-foreground-contrast mb-1">
              {{ $t('device-detail.model') }}
            </dt>
            <dd class="font-medium">
              {{ deviceDetail.device?.model || '-' }}
            </dd>
          </div>

          <div class="flex flex-col">
            <dt class="text-xs font-medium text-prim-col-foreground-contrast mb-1">
              {{ $t('device-detail.serial-number') }}
            </dt>
            <dd class="font-medium">
              {{ deviceDetail.serial_number || '-' }}
            </dd>
          </div>
        </dl>
      </div>
    </section>

    <section class="bg-prim-col-foreground-1 rounded-xl">
      <div class="p-4">
        <div class="mb-4">
          <h2 class="text-lg leading-6 font-medium">
            {{ $t('device-detail.all-metrics') }}
          </h2>
          <div class="text-xs text-prim-col-foreground-contrast mt-1">
            {{ $t('device-detail.last-update') }} {{ DateTime.fromISO(deviceLastMetricsData.time).toFormat('d.M.yyyy HH:mm:ss') }}
          </div>
        </div>
        <dl class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div v-for="metric in Object.values(deviceLastMetricsData.metrics).filter(m => !forbiddenMetrics.includes(m.name))" :key="metric.name" class="flex flex-col">
            <dt class="text-xs font-medium text-prim-col-foreground-contrast mb-1">
              <!-- {{ $t(`metrics.${metric.name}`) }} -->
              {{ metric.name }}
            </dt>
            <dd class="font-medium">
              {{ metric.value }}
              <span v-if="metric.unit"> {{ metric.unit }} </span>
            </dd>
          </div>
        </dl>
      </div>
    </section>

    <section class="bg-prim-col-foreground-1 rounded-xl">
      <div class="p-4">
        <h2 class="text-lg leading-6 font-medium">
          {{ $t('device-detail.history-charts') }}
        </h2>
        <DeviceDetailMetricChartGroup
          :stats-data="deviceStats"
          :installation-id="installationId"
          :device-instance-id="deviceId"
          :forbidden-metrics="forbiddenMetrics"
        />
      </div>
    </section>
  </div>
</template>