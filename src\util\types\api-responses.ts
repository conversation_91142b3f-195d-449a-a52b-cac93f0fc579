import type { UserData } from '@/stores/auth-store';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';

export type OAuthTokenResponse = {
  token_type: string,
  expires_in: number,
  access_token: string,
  refresh_token: string,
}

export type OAuthUserResponse = {
  data: UserData
}

export type Link = {
  first: string;
  last: string;
  prev: string | null;
  next: string | null;
}

export type ApiUsersResponse = {
  data: UserData[];
  links: Link;
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    links: Link[];
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export type ApiRolesResponse = {
  data: RoleData[];
}

export type ApiPermissionsResponse = {
  data: PermissionData[];
}

export type ApiMetricKeys = 'pv1_voltage'|'pv1_current'|'pv1_power'|'pv2_voltage'|'pv2_current'|'pv2_power'|'pv3_voltage'|'pv3_current'|'pv3_power'|'pv4_voltage'|'pv4_current'|'pv4_power'|'pv_mode'|'atk_solar_energy'|'grid_l1_voltage'|'grid_l1_current'|'grid_l1_frequency'|'grid_l1_power'|'grid_l2_voltage'|'grid_l2_current'|'grid_l2_frequency'|'grid_l2_power'|'grid_l3_voltage'|'grid_l3_current'|'grid_l3_frequency'|'grid_l3_power'|'grid_mode'|'total_inv_power'|'ac_active_power'|'ac_reactive_power'|'ac_apparent_power'|'backup_l1_load_voltage'|'backup_l1_load_current'|'backup_l1_load_frequency'|'backup_l1_load_mode'|'backup_l1_load_power'|'backup_l2_load_voltage'|'backup_l2_load_current'|'backup_l2_load_frequency'|'backup_l2_load_power'|'backup_l3_load_voltage'|'backup_l3_load_current'|'backup_l3_load_frequency'|'backup_l3_load_power'|'l1_load_power'|'l2_load_power'|'l3_load_power'|'backup_total_load_power'|'total_load_power'|'ups_load_percent'|'air_temperature'|'module_temperature'|'radiator_temperature'|'battery_voltage'|'battery_current'|'atk_battery_power'|'battery_mode'|'warning_code'|'safety_country'|'work_mode'|'inverter_working_state'|'operation_mode'|'error_message'|'cpld_warning_code'|'w_charger_ctrl_flg'|'meter_connect_status'|'meter_communication_status'|'meter_active_power_l1'|'meter_active_power_l2'|'meter_active_power_l3'|'atk_grid_power'|'meter_total_reactive_power'|'meter_power_factor_l1'|'meter_power_factor_l2'|'meter_power_factor_l3'|'meter_power_factor'|'meter_frequency'|'atk_grid_energy_sell'|'atk_grid_energy_buy'|'bms_status'|'bms_pack_temperature'|'bms_charge_i_max'|'bms_discharge_i_max'|'bms_error_code_l'|'atk_battery_charge'|'bms_state_of_health'|'bms_warning_code_l'|'bms_max_cell_temperature_id'|'bms_min_cell_temperature_id'|'bms_max_cell_voltage_id'|'bms_min_cell_voltage_id'|'bms_max_cell_temperature'|'bms_min_cell_temperature'|'bms_max_cell_voltage'|'bms_min_cell_voltage'|'all_data'|'atk_solar_power'|'atk_home_power'|'small_packet_interval'|'safety_country_text';

export type MetricValueOverrides = {
  safety_country_text: string;
};

export type MetricValueType<K extends ApiMetricKeys> =
  K extends keyof MetricValueOverrides ? MetricValueOverrides[K] : number;

export type MetricMap = {
  [K in ApiMetricKeys]: {
    name: K;
    time: string;
    type: string;
    unit: string;
    value: MetricValueType<K>;
  };
};

export type ApiMetric = MetricMap[ApiMetricKeys];

export type InverterCurrentDataResponse = {
  device_id: string;
  metrics: ApiMetric[];
  time: string;
  expired?: boolean;
}[]

export type InverterCurrentDataTransformed = {
  metrics: MetricMap;
  time: string;
  expired?: boolean;
}

export type ApiRangeMultiDataResponse = {
  data: {
    type: string;
    unit: string;
    data: {
      t: string;
      v: number;
      avg?: number;
      delta?: number;
    }[]
  };
  metric: ApiMetricKeys;
}[]

export type DeltaDataApiResponse = {
  device_id: string,
  metrics: {
    metric: string,
    value: number,
  }
}[]

export type DeltaData = Record<ApiMetricKeys, {value: number, unit: string}>;

export enum WorkMode {
  STANDBY = 0,
  ON_GRID = 1,
  OFF_GRID = 2,
  ERROR= 3,
  UPGRADING = 4,
  POWER_ON_SELF_CHECK = 5,
}

export enum WorkModeINVT {
  INITIALIZATION = 0,
  STANDBY = 1,
  NORMAL = 2,
  FAULTY = 3,
  PROGRAMMING = 4,
  OFF_GRID = 5,
  NO_VOLTAGE = 6,
}

export type DeviceLastMetricsApiResponse = {
  metrics: MetricMap;
  time: string;
}

export type DeviceStatsMetricsApiResponse = {
  datapoints: number;
  from: string;
  metrics: {
    name: string,
    type: 'float' | 'float_counter',
    unit: string,
    from?: string,
    to?: string,
  }[],
  time: string;
}