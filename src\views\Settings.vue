<script setup lang="ts">
import { Lock, Settings, ListChecks } from 'lucide-vue-next';
import { useRoute } from 'vue-router';
import { routeMap } from '@/router/routes';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import { ref } from 'vue';
import { customAxios } from '@/util/axios';
import axios from 'axios';

const route = useRoute();

initializeBasicBreadcrumbBehaviour(routeMap.settings.meta.i18nTitle, routeMap.settings.name, true, route);

const appVersion = ref('unset');
const apiVersion = ref('unset');

try {
  const [versionResponse, apiVersionResponse] = await Promise.all([
    axios.get('/version.json'),
    customAxios.get('/api-version'),
  ]);
  appVersion.value = versionResponse.data.number;
  apiVersion.value = apiVersionResponse.data;
} catch {
  //
}

</script>

<template>
  <div class="grid md:grid-cols-[16rem_1fr] gap-4 xl:max-w-[80vw] 2xl:max-w-[50vw] mx-auto">
    <div class="flex flex-col w-full">
      <section class="w-full bg-prim-col-foreground-1 drop-shadow-lg rounded-xl">
        <header class="font-bold px-3 pt-2 pb-1">
          Personal
        </header>
        <div class="h-px w-full bg-prim-col-foreground-2/50" />
        <nav class="w-full">
          <ul class="flex flex-col w-full gap-1 p-1 [&_a]:rounded-lg">
            <li class="w-full">
              <router-link
                exact-active-class="bg-prim-col-selected-1/40"
                class="flex items-center gap-1 px-3 py-1 hover:bg-prim-col-selected-1/40"
                :to="{name: routeMap.settings.children.changePassword.name}"
              >
                <Lock class="h-4 w-4 text-prim-col-foreground-contrast" />
                <span class="block font-medium">{{ $t('settings.pw-change') }}</span>
              </router-link>
            </li>
            <li class="w-full">
              <router-link
                exact-active-class="bg-prim-col-selected-1/40"
                class="flex items-center gap-1 px-3 py-1 hover:bg-prim-col-selected-1/40"
                :to="{name: routeMap.settings.children.changelog.name}"
              >
                <ListChecks class="h-4 w-4 text-prim-col-foreground-contrast" />
                <span class="block font-medium">Changelog</span>
              </router-link>
            </li>
          </ul>
        </nav>
        <div class="px-4 py-2 bg-black/10 rounded-b-xl flex flex-col gap-1">
          <div><span class="text-gray-400">{{ $t('misc.app-version') }}:</span> {{ appVersion }}</div>
          <div><span class="text-gray-400">{{ $t('misc.api-version') }}:</span> {{ apiVersion }}</div>
        </div>
      </section>
    </div>
    <div
      class="bg-prim-col-foreground-1 rounded-xl"
      :class="[
        route.name === routeMap.settings.name ? 'h-fit w-full' : 'w-full flex justify-center md:block md:w-fit max-h-[85dvh] overflow-y-auto overflow-x-hidden drop-shadow-lg' ]"
    >
      <suspense>
        <router-view />
      </suspense>
      <div
        v-if="route.name === routeMap.settings.name"
        class="py-4 w-full h-full flex flex-col justify-center items-center text-center text-gray-300"
      >
        <Settings class="h-20 w-20" />
        <div class="font-bold">
          {{ $t('misc.choose-from-menu') }}
        </div>
      </div>
    </div>
  </div>
</template>
