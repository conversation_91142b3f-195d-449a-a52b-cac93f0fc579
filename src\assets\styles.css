@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-*: initial;
  --font-sans: Roboto, sans-serif;

  --breakpoint-*: initial;
  --breakpoint-lw: 390px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1440px;
  --breakpoint-3xl: 1700px;
  --breakpoint-4xl: 1900px;
  --breakpoint-5xl: 2200px;

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-prim-col-1: rgb(var(--prim-col-1));
  --color-prim-col-2: rgb(var(--prim-col-2));
  --color-prim-col-foreground-1: rgb(var(--prim-col-foreground-1));
  --color-prim-col-foreground-2: rgb(var(--prim-col-foreground-2));
  --color-prim-col-foreground-contrast: rgb(
    var(--prim-col-foreground-contrast)
  );
  --color-prim-col-selected-1: rgb(var(--prim-col-selected-1));
  --color-prim-col-selected-2: rgb(var(--prim-col-selected-2));
  --color-atk-orange: var(--atk-orange);
  --color-atk-orange-darker: var(--atk-orange-darker);

  --radius-xl: calc(var(--radius) + 4px);
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-collapsible-down: collapsible-down 0.2s ease-in-out;
  --animate-collapsible-up: collapsible-up 0.2s ease-in-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-lw)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --ring: 212.7 26.8% 83.9%;

    --radius: 0.5rem;
  }

  .dark {}
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    color: black;
    padding: 0!important;
    &.dark {
      color: white;
    }
  }
}

@layer base {

  .custom-scrollbar-1 {
    padding-right: 4px;
    --sb-track-color: oklch(0 0 0 / 0);
    --sb-thumb-color: #2e4d58;
    --sb-size: 5px;

    &::-webkit-scrollbar {
      width: var(--sb-size)
    }

    &::-webkit-scrollbar-track {
      background: var(--sb-track-color);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--sb-thumb-color);
      border-radius: 3px;

    }

    @supports not selector(::-webkit-scrollbar) {
      & {
        scrollbar-color: var(--sb-thumb-color)
        var(--sb-track-color);
      }
    }
  }
}

@layer base {
  .absolute-center {
    @apply absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2
  }
}

@layer base {
  .dark {
    input[type="date"] {
      &::-webkit-calendar-picker-indicator {
        background-color: #79a7b8;
        border-radius: 8px;
        padding: 5px;
        cursor: pointer;
        color-scheme: dark;
      }

      &.disable-icon-bg {
        background-color: unset;
      }
    }
  }

  input[type="date"] {
    &::-webkit-calendar-picker-indicator {
      background-color: #79a7b8;
      border-radius: 8px;
      padding: 5px;
      cursor: pointer;
    }

    &.disable-icon-bg {
      &::-webkit-calendar-picker-indicator {
        background-color: unset;
      }
    }
  }
}


.dark .vue-apexcharts tspan {
  fill: #e0e0e0 !important;
}

.dark .apexcharts-gridline {
  stroke: #e0e0e05e;
}

.dark .apexcharts-grid-borders line {
  stroke: #e0e0e05e;
}

.dark .apexcharts-legend-text {
  color: #e0e0e0 !important;
}

.dark .apexcharts-title-text {
  fill: #e0e0e0 !important;
}

.dark .apexcharts-tooltip {
  background: rgba(var(--prim-col-1) / .6) !important;
  border-color: rgba(var(--prim-col-1) / .6) !important;
}

.dark .apexcharts-tooltip-title {
  background: rgba(var(--prim-col-1) / 0.9) !important;
  border-color: rgba(var(--prim-col-1) / 0.9) !important;
}

.dark .apexcharts-xaxistooltip {
  background: rgba(var(--prim-col-1) / 0.7) !important;
  border-color:rgba(var(--prim-col-1) / 0.7) !important;
  color: white;
}

.dark .apexcharts-menu.apexcharts-menu-open {
  background: rgba(var(--prim-col-1) / 0.7) !important;
}
