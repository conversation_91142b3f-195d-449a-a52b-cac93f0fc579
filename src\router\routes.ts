import { mergeDeep } from '@/util/objects';
import { Layouts } from '@/util/types/layouts';
import type { RouteRecordRaw } from 'vue-router';

export type CustomRouteMeta = {
  i18nTitle: string,
  layout?: () => Layouts,
  authNotRequired?: boolean
  disabledForVerified?: boolean,
  redirect?: Record<string, string>
}

export type ARMRouteMap = {
  [key: string]: Pick<RouteRecordRaw, 'path' | 'name' | 'redirect'> & {
    meta: CustomRouteMeta,
    children?: ARMRouteMap
  };
}

export const routeMap = {
  home: {
    path: '/',
    name: 'home',
    meta: {
      i18nTitle: 'misc.dashboard',
      layout: () => Layouts.BASIC_WITH_MENU,
    },
    children: {
      dashboardInstallations: {
        path: '',
        name: 'installationDashboard',
        meta: {
          i18nTitle: 'misc.dashboard',
        },
      },
      installation: {
        path: 'installations/:installationId',
        name: 'installation',
        meta: {
          i18nTitle: 'installation.title-plural',
        },
      },
      deviceDetail: {
        path: 'installations/:installationId/device/:deviceId',
        name: 'deviceDetail',
        meta: {
          i18nTitle: 'devices.title',
        },
      },
    },
  },
  reports: {
    path: '/reports',
    name: 'reports',
    meta: {
      i18nTitle: 'reports.title',
      layout: () => Layouts.BASIC_WITH_MENU,
    },
  },
  alerts: {
    path: '/alerts',
    name: 'alerts',
    meta: {
      i18nTitle: 'alerts.title',
      layout: () => Layouts.BASIC_WITH_MENU,
    },
  },
  monitoring: {
    path: '/monitoring',
    name: 'monitoring',
    meta: {
      i18nTitle: 'monitoring.title',
      layout: () => Layouts.BASIC_WITH_MENU,
    },
  },
  register: {
    path: '/register',
    name: 'register',
    meta: {
      i18nTitle: 'register.create-account',
      layout: () => Layouts.BASIC_WITH_MENU,
      authNotRequired: true,
      disabledForVerified: true,
    },
  },
  verify: {
    path: '/verify/:token',
    name: 'verify',
    meta: {
      i18nTitle: 'register.verify-email',
      layout: () => Layouts.EMPTY,
      authNotRequired: true,
      disabledForVerified: true,
    },
  },
  resetPassword: {
    path: '/password/reset/:token',
    name: 'user.reset-password',
    meta: {
      layout: () => Layouts.EMPTY,
      i18nTitle: 'login.reset-password',
      authNotRequired: true,
      disabledForVerified: true,
    },
  },
  forgotPassword: {
    path: '/forgot-password',
    name: 'user.forgot-password',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'login.forgotten-password',
      authNotRequired: true,
      disabledForVerified: true,
    },
  },
  management: {
    path: '/manage',
    name: 'manage',
    children: {
      users: {
        path: 'users',
        name: 'manage.users',
        meta: {
          i18nTitle: 'user-management.title',
        },
      },
      roles: {
        path: 'roles',
        name: 'manage.roles',
        meta: {
          i18nTitle: 'role-management.title',
        },
      },
    },
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: '',
    },
  },
  settings: {
    path: '/settings',
    name: 'settings',
    children: {
      changePassword: {
        path: 'password',
        name: 'settings.password',
        meta: {
          i18nTitle: 'settings.change-password',
        },
      },
      changelog: {
        path: 'changelog',
        name: 'settings.changelog',
        meta: {
          i18nTitle: 'settings.changelog',
        },
      },
    },
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'misc.settings',
    },
  },
  verifyMail: {
    //  TODO: VIEW FOR THIS ROUTE
    path: '/not-verified',
    name: 'user.not-verified',
    meta: {
      layout: () => Layouts.EMPTY,
      i18nTitle: 'login.reset-password',
      authNotRequired: true,
      disabledForVerified: true,
    },
  },
  notFound: {
    path: '/:pathMatch(.*)*', // Wildcard route for 404
    name: 'not-found',
    redirect: { path: '/' },
    meta: {
      i18nTitle: 'misc.not-found',
    },
  },
} as const satisfies ARMRouteMap;

export const routeIndex = {
  home: {
    ...routeMap.home,
    component: () => import('@/views/HomePage.vue'),
    children: [
      {
        ...routeMap.home.children.dashboardInstallations,
        component: () => import('@/pages/DashboardPage.vue'),
      },
      {
        ...routeMap.home.children.installation,
        component: () => import('@/data-layers/InstallationPageDataLayer.vue'),
      },
      {
        ...routeMap.home.children.deviceDetail,
        component: () => import('@/data-layers/DevicePageDataLayer.vue'),
      },
    ],
  },
  reports: {
    ...routeMap.reports,
    component: () => import('@/views/Reports.vue'),
  },
  alerts: {
    ...routeMap.alerts,
    component: () => import('@/views/Alerts.vue'),
  },
  monitoring: {
    ...routeMap.monitoring,
    component: () => import('@/views/Monitoring.vue'),
  },
  register: {
    ...routeMap.register,
    component: () => import('@/views/Register.vue'),
  },
  verify: {
    ...routeMap.verify,
    component: () => import('@/views/Verify.vue'),
  },
  resetPassword: {
    ...routeMap.resetPassword,
    component: () => import('@/views/ResetPassword.vue'),
  },
  forgotPassword: {
    ...routeMap.forgotPassword,
    component: () => import('@/views/ForgotPassword.vue'),
  },
  management: {
    ...routeMap.management,
    component: () => import('@/views/Management.vue'),
    redirect: `/manage/${routeMap.management.children.users.path}`,
    children: [
      {
        ...routeMap.management.children.users,
        component: () => import('@/pages/UserManagement.vue'),
      },
      {
        ...routeMap.management.children.roles,
        component: () => import('@/pages/RoleManagement.vue'),
      },
    ],
  },
  settings: {
    ...routeMap.settings,
    component: () => import('@/views/Settings.vue'),
    children: [
      {
        ...routeMap.settings.children.changePassword,
        component: () => import('@/pages/ChangePassword.vue'),
      },
      {
        ...routeMap.settings.children.changelog,
        component: () => import('@/pages/Changelog.vue'),
      },
    ],
  },
  verifyMail: {
    ...routeMap.verifyMail,
    component: () => import('@/views/VerifyEmail.vue'),
  },
  notFound: {
    ...routeMap.notFound,
  },
  // not_found: {
  //   path: '/:NotFound(.*)*',
  //   name: 'not_found',
  //   component: () => import('@/views/NotFound.vue'),
  //   children: {},
  //   meta: {
  //     requiresAuth: false,
  //     name_public: 'paging.not_found',
  //   },
  // },
};

const routesArray = Object.values(mergeDeep({}, routeIndex)).map(elm => {
  // @ts-expect-error false positive
  if (elm.children) {
    // @ts-expect-error false positive
    elm.children = Object.values(elm.children);
  }
  return elm;
});

const mapFilter = (route: RouteRecordRaw) => {
  if (route.meta?.enabled === false) {
    return false;
  }
  if (!route.children) {
    return true;
  }
  route.children = route.children.filter(mapFilter);
  return true;
};

// @ts-expect-error false positive
export const routes: Array<RouteRecordRaw> = routesArray.filter(mapFilter);
