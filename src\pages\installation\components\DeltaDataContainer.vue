<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import type { InverterCurrentDataTransformed, DeltaData, ApiMetricKeys } from '@/util/types/api-responses';
import GeneralPopover from '@/components/global/GeneralPopover.vue';
import DayPicker from '@/components/global/DayPicker.vue';
import { DateTime } from 'luxon';
import ButtonVariant1 from '@/components/ButtonVariant1.vue';
import { CalendarIcon, ChevronLeftIcon, ChevronRightIcon, BarChart3Icon, PieChartIcon } from 'lucide-vue-next';
import GenericGridSummary from './GenericGridSummary.vue';
import EnergyPieCharts from './EnergyPieCharts.vue';
import { customAxios } from '@/util/axios';
import { useRoute } from 'vue-router';

interface Props {
  deltaData: DeltaData,
  inverterCurrentData: InverterCurrentDataTransformed,
}

enum SummaryType {
  SPECIFIC_DAY,
  MONTH,
  YEAR,
  TOTAL,
}

enum DisplayType {
  GRID,
  CHARTS,
}

const props = defineProps<Props>();
const route = useRoute();

const currentViewType = ref(SummaryType.SPECIFIC_DAY);
const displayType = ref(DisplayType.GRID);
const customDay = ref<string>(DateTime.now().toISODate()!);
const customMonth = ref<string>(DateTime.now().toFormat('yyyy-MM'));
const customYear = ref<string>(DateTime.now().toFormat('yyyy'));
const customDeltaData = ref<DeltaData>({} as DeltaData);

const metricsDeltaWorker = new Worker(new URL('@/workers/metrics-delta-multi.worker.ts', import.meta.url), {
  type: 'module',
});

metricsDeltaWorker.onmessage = (event: MessageEvent<DeltaData>) => {
  customDeltaData.value = event.data;
};

const fetchCustomDeltaData = async(start: string, end: string) => {
  try {
    const axiosRes = await customAxios.get<{metric: string, value: number}[]>(`metrics/${route.params.installationId}/inverter/delta`, {
      params: {
        type: ['atk_solar_energy', 'atk_grid_energy_buy', 'atk_grid_energy_sell'] as ApiMetricKeys[],
        start,
        end,
      },
    });
    metricsDeltaWorker.postMessage(axiosRes.data);
  } catch {
    Object.keys(customDeltaData.value).forEach(k => {
      delete customDeltaData.value[k as keyof typeof customDeltaData.value];
    });
  }
};

const onCustomDayChanged = (dateStart: string) => {
  customDay.value = dateStart;
  currentViewType.value = SummaryType.SPECIFIC_DAY;
};

const navigatePrevious = () => {
  switch (currentViewType.value) {
    case SummaryType.SPECIFIC_DAY:
      customDay.value = DateTime.fromISO(customDay.value).minus({ day: 1 }).toISODate()!;
      break;
    case SummaryType.MONTH:
      customMonth.value = DateTime.fromISO(customMonth.value + '-01').minus({ month: 1 }).toFormat('yyyy-MM');
      break;
    case SummaryType.YEAR:
      customYear.value = DateTime.fromISO(customYear.value + '-01-01').minus({ year: 1 }).toFormat('yyyy');
      break;
  }
};

const navigateNext = () => {
  const now = DateTime.now();
  switch (currentViewType.value) {
    case SummaryType.SPECIFIC_DAY: {
      const nextDay = DateTime.fromISO(customDay.value).plus({ day: 1 });
      if (nextDay <= now) {
        customDay.value = nextDay.toISODate()!;
      }
      break;
    }
    case SummaryType.MONTH: {
      const nextMonth = DateTime.fromISO(customMonth.value + '-01').plus({ month: 1 });
      if (nextMonth <= now) {
        customMonth.value = nextMonth.toFormat('yyyy-MM');
      }
      break;
    }
    case SummaryType.YEAR: {
      const nextYear = DateTime.fromISO(customYear.value + '-01-01').plus({ year: 1 });
      if (nextYear <= now) {
        customYear.value = nextYear.toFormat('yyyy');
      }
      break;
    }
  }
};

// Computed properties pre dáta
const currentGridData = computed(() => {
  switch (currentViewType.value) {
    case SummaryType.SPECIFIC_DAY:
      if (customDay.value === DateTime.now().toISODate()) {
        // Dnešný deň - použiť deltaData z props
        return {
          producedEnergy: props.deltaData?.atk_solar_energy,
          energyFromNetwork: props.deltaData?.atk_grid_energy_buy,
          energyToNetwork: props.deltaData?.atk_grid_energy_sell,
          houseConsumption: props.deltaData?.atk_solar_energy && props.deltaData?.atk_grid_energy_sell && props.deltaData?.atk_grid_energy_buy
            ? {
              value: Math.max(0, props.deltaData.atk_solar_energy.value - props.deltaData.atk_grid_energy_sell.value + props.deltaData.atk_grid_energy_buy.value),
              unit: props.deltaData.atk_solar_energy.unit
            }
            : undefined
        };
      } else {
        // Iný deň - použiť customDeltaData
        return {
          producedEnergy: customDeltaData.value?.atk_solar_energy,
          energyFromNetwork: customDeltaData.value?.atk_grid_energy_buy,
          energyToNetwork: customDeltaData.value?.atk_grid_energy_sell,
          houseConsumption: customDeltaData.value?.atk_solar_energy && customDeltaData.value?.atk_grid_energy_sell && customDeltaData.value?.atk_grid_energy_buy
            ? {
              value: Math.max(0, customDeltaData.value.atk_solar_energy.value - customDeltaData.value.atk_grid_energy_sell.value + customDeltaData.value.atk_grid_energy_buy.value),
              unit: customDeltaData.value.atk_solar_energy.unit
            }
            : undefined
        };
      }
    case SummaryType.TOTAL:
      return {
        producedEnergy: props.inverterCurrentData?.metrics?.atk_solar_energy,
        energyFromNetwork: props.inverterCurrentData?.metrics?.atk_grid_energy_buy,
        energyToNetwork: props.inverterCurrentData?.metrics?.atk_grid_energy_sell,
        houseConsumption: props.inverterCurrentData?.metrics?.atk_solar_energy && props.inverterCurrentData?.metrics?.atk_grid_energy_sell && props.inverterCurrentData?.metrics?.atk_grid_energy_buy
          ? {
            value: Math.round((props.inverterCurrentData.metrics.atk_solar_energy.value - props.inverterCurrentData.metrics.atk_grid_energy_sell.value + props.inverterCurrentData.metrics.atk_grid_energy_buy.value) * 1000) / 1000,
            unit: props.inverterCurrentData.metrics.atk_solar_energy.unit
          }
          : undefined
      };
    default:
      // Pre MONTH a YEAR použiť customDeltaData
      return {
        producedEnergy: customDeltaData.value?.atk_solar_energy,
        energyFromNetwork: customDeltaData.value?.atk_grid_energy_buy,
        energyToNetwork: customDeltaData.value?.atk_grid_energy_sell,
        houseConsumption: customDeltaData.value?.atk_solar_energy && customDeltaData.value?.atk_grid_energy_sell && customDeltaData.value?.atk_grid_energy_buy
          ? {
            value: Math.max(0, customDeltaData.value.atk_solar_energy.value - customDeltaData.value.atk_grid_energy_sell.value + customDeltaData.value.atk_grid_energy_buy.value),
            unit: customDeltaData.value.atk_solar_energy.unit
          }
          : undefined
      };
  }
});

// Watchers pre fetch dát
watch([customDay, customMonth, customYear, currentViewType], () => {
  if (currentViewType.value === SummaryType.SPECIFIC_DAY && customDay.value !== DateTime.now().toISODate()) {
    const start = DateTime.fromISO(customDay.value).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).toISO();
    const end = DateTime.fromISO(customDay.value).set({ hour: 23, minute: 59, second: 59, millisecond: 999 }).toISO();
    fetchCustomDeltaData(start!, end!);
  } else if (currentViewType.value === SummaryType.MONTH) {
    const start = DateTime.fromISO(customMonth.value + '-01').startOf('month').toISO();
    const end = DateTime.fromISO(customMonth.value + '-01').endOf('month').toISO();
    fetchCustomDeltaData(start!, end!);
  } else if (currentViewType.value === SummaryType.YEAR) {
    const start = DateTime.fromISO(customYear.value + '-01-01').startOf('year').toISO();
    const end = DateTime.fromISO(customYear.value + '-01-01').endOf('year').toISO();
    fetchCustomDeltaData(start!, end!);
  }
});

</script>

<template>
  <div>
    <!-- Menu s justify-between layoutom -->
    <div class="w-full flex justify-between items-center gap-2 mt-1 mb-0.5">
      <!-- Ľavá strana - časové možnosti -->
      <div class="flex items-center gap-2">
        <!-- Navigačné šípky -->
        <div v-if="currentViewType !== SummaryType.TOTAL" class="flex items-center gap-1">
          <ButtonVariant1
            class="rounded-md p-1.5 text-xs transition-all duration-150"
            @click="navigatePrevious"
          >
            <ChevronLeftIcon class="h-3.5 w-3.5" />
          </ButtonVariant1>
          <ButtonVariant1
            class="rounded-md p-1.5 text-xs transition-all duration-150"
            @click="navigateNext"
          >
            <ChevronRightIcon class="h-3.5 w-3.5" />
          </ButtonVariant1>
        </div>

        <!-- Časové možnosti -->
        <div class="flex items-center gap-1">
          <GeneralPopover
            align="start"
            content-classes="bg-prim-col-foreground-1 p-2.5 rounded-xl"
          >
            <template #popover-trigger>
              <ButtonVariant1
                class="rounded-lg py-1.5 px-2 text-xs break-word cursor-pointer relative transition-all duration-150 inline-flex gap-1"
                :active="currentViewType === SummaryType.SPECIFIC_DAY"
              >
                <CalendarIcon class="h-3.5 w-3.5" />
                {{ currentViewType === SummaryType.SPECIFIC_DAY ? DateTime.fromISO(customDay).toFormat('dd.MM.yyyy') : $t('installation.pick-a-day') }}
              </ButtonVariant1>
            </template>
            <template #popover-content>
              <DayPicker
                :initial-day="customDay"
                :max="DateTime.now().toISODate()"
                @day-changed="onCustomDayChanged"
              />
            </template>
          </GeneralPopover>

          <ButtonVariant1
            class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150"
            :active="currentViewType === SummaryType.MONTH"
            @click="currentViewType = SummaryType.MONTH"
          >
            Mesiac
          </ButtonVariant1>

          <ButtonVariant1
            class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150"
            :active="currentViewType === SummaryType.YEAR"
            @click="currentViewType = SummaryType.YEAR"
          >
            Rok
          </ButtonVariant1>

          <ButtonVariant1
            class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150"
            :active="currentViewType === SummaryType.TOTAL"
            @click="currentViewType = SummaryType.TOTAL"
          >
            {{ $t('installation.total-summary') }}
          </ButtonVariant1>
        </div>
      </div>

      <!-- Pravá strana - typ zobrazenia -->
      <div class="flex items-center gap-1">
        <ButtonVariant1
          class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150 inline-flex gap-1"
          :active="displayType === DisplayType.GRID"
          @click="displayType = DisplayType.GRID"
        >
          <BarChart3Icon class="h-3.5 w-3.5" />
          Dlaždice
        </ButtonVariant1>

        <ButtonVariant1
          class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150 inline-flex gap-1"
          :active="displayType === DisplayType.CHARTS"
          @click="displayType = DisplayType.CHARTS"
        >
          <PieChartIcon class="h-3.5 w-3.5" />
          Koláčové grafy
        </ButtonVariant1>
      </div>
    </div>
    <!-- Obsah -->
    <transition
      mode="out-in"
      name="slide-up"
    >
      <div v-if="displayType === DisplayType.GRID">
        <GenericGridSummary
          :grid-data="currentGridData"
          :animation-speed="currentViewType === SummaryType.SPECIFIC_DAY && customDay === DateTime.now().toISODate() ? 1 : 0"
        />
      </div>
      <div v-else-if="displayType === DisplayType.CHARTS">
        <EnergyPieCharts :grid-data="currentGridData" />
      </div>
    </transition>
  </div>
</template>

<style scoped lang="css">

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.25s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

</style>
