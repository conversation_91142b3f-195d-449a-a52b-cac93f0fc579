<script lang="ts" setup>
import { ref } from 'vue';
import TodayGrid from '@/pages/installation/components/TodayGrid.vue';
import TotalGrid from '@/pages/installation/components/TotalGrid.vue';
import type { InverterCurrentDataTransformed, DeltaData } from '@/util/types/api-responses';
import GeneralPopover from '@/components/global/GeneralPopover.vue';
import DayPicker from '@/components/global/DayPicker.vue';
import { DateTime } from 'luxon';
import SpecificDayGrid from './SpecificDayGrid.vue';
import ButtonVariant1 from '@/components/ButtonVariant1.vue';
import { CalendarIcon } from 'lucide-vue-next';

interface Props {
  deltaData: DeltaData,
  inverterCurrentData: InverterCurrentDataTransformed,
}

enum SummaryType {
  TODAY,
  SPECIFIC_DAY,
  TOTAL,
}

const currentViewType = ref(SummaryType.TODAY);
const customDay = ref<string>();

const onCustomDayChanged = (dateStart: string) => {
  customDay.value = dateStart;
  currentViewType.value = SummaryType.SPECIFIC_DAY;
};

defineProps<Props>();

</script>

<template>
  <div>
    <div class="w-full flex justify-center items-center gap-2 [&>div]:cursor-pointer [&>div]:select-none mt-1 mb-0.5">
      <ButtonVariant1
        class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150"
        :active="currentViewType === SummaryType.TODAY"
        @click="currentViewType = SummaryType.TODAY"
      >
        {{ $t('installation.today-summary') }}
      </ButtonVariant1>
      <ButtonVariant1
        class="rounded-md px-2 py-1.5 text-xs break-word relative transition-all duration-150"
        :active="currentViewType === SummaryType.TOTAL"
        @click="currentViewType = SummaryType.TOTAL"
      >
        {{ $t('installation.total-summary') }}
      </ButtonVariant1>
      <GeneralPopover
        align="end"
        content-classes="bg-prim-col-foreground-1 p-2.5 rounded-xl"
      >
        <template #popover-trigger>
          <ButtonVariant1
            class="rounded-lg py-1.5 px-2 text-xs break-word cursor-pointer relative transition-all duration-150 inline-flex gap-1"
            :active="currentViewType === SummaryType.SPECIFIC_DAY"
          >
            <CalendarIcon class="h-3.5 w-3.5" />
            {{ currentViewType === SummaryType.SPECIFIC_DAY ? DateTime.fromISO(customDay!).toFormat('dd.MM.yyyy') : $t('installation.pick-a-day') }}
          </ButtonVariant1>
        </template>
        <template #popover-content>
          <DayPicker
            :initial-day="customDay ?? DateTime.now().minus({day: 1}).toISODate()"
            :max="DateTime.now().minus({day: 1}).toISODate()"
            @day-changed="onCustomDayChanged"
          />
        </template>
      </GeneralPopover>
    </div>
    <transition
      mode="out-in"
      name="slide-up"
    >
      <TodayGrid
        v-if="currentViewType === SummaryType.TODAY"
        :delta-data="deltaData"
        :inverter-current-data="inverterCurrentData"
      />
      <TotalGrid
        v-else-if="currentViewType === SummaryType.TOTAL"
        :inverter-current-data="inverterCurrentData"
      />
      <SpecificDayGrid
        v-else-if="currentViewType === SummaryType.SPECIFIC_DAY"
        :date="customDay!"
      />
    </transition>
  </div>
</template>

<style scoped lang="css">

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.25s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

</style>
