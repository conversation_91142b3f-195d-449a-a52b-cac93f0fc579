<script lang="ts" setup>
import { CircleUser } from 'lucide-vue-next';
import { useRoute, useRouter } from 'vue-router';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo.svg';
import HouseHoldPicker from '@/components/global/HouseHoldPicker.vue';
import MobileSidebarSheet from '@/components/global/MobileSidebarSheet.vue';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import { isLightModeEnabled } from '@/composables/theme';
import { routeMap } from '@/router/routes';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/shadcn-components/ui/dropdown-menu';
import { useAuthStore } from '@/stores/auth-store';

const { logout } = useAuthStore();
const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const onLogoutPress = async() => {
  await router.replace({ path: routeMap.home.path });
  logout();
};

const onChangeMode = () => {
  isLightModeEnabled.value = !isLightModeEnabled.value;
};

</script>

<template>
  <header class="sticky top-0 z-30 py-2 flex flex-col gap-2 sm:py-4 border-b border-prim-col-foreground-2 bg-prim-col-2 px-4 sm:h-auto sm:px-6">
    <div class="flex items-center gap-4 relative">
      <mobile-sidebar-sheet />
      <div v-if="route.name === routeMap.home.children.installation.name">
        <HouseHoldPicker />
      </div>
      <div class="hidden sm:block absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2">
        <SolarCloudLogo class="h-9 w-auto [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div class="ml-auto mr-0 flex items-center gap-2">
        <button
          id="theme-toggle"
          class="hidden sm:block theme-toggle"
          title="Toggles light &amp; dark"
          aria-label="light"
          aria-live="polite"
          @click="onChangeMode"
        >
          <svg
            class="sun-and-moon"
            aria-hidden="true"
            width="30"
            height="30"
            viewBox="0 0 24 24"
          >
            <mask
              id="moon-mask"
              class="moon"
            >
              <rect
                x="0"
                y="0"
                width="100%"
                height="100%"
                fill="white"
              />
              <circle
                cx="24"
                cy="10"
                r="6"
                fill="black"
              />
            </mask>
            <circle
              class="sun"
              cx="12"
              cy="12"
              r="6"
              mask="url(#moon-mask)"
              fill="currentColor"
            />
            <g
              class="sun-beams"
              stroke="currentColor"
            >
              <line
                x1="12"
                y1="1"
                x2="12"
                y2="3"
              />
              <line
                x1="12"
                y1="21"
                x2="12"
                y2="23"
              />
              <line
                x1="4.22"
                y1="4.22"
                x2="5.64"
                y2="5.64"
              />
              <line
                x1="18.36"
                y1="18.36"
                x2="19.78"
                y2="19.78"
              />
              <line
                x1="1"
                y1="12"
                x2="3"
                y2="12"
              />
              <line
                x1="21"
                y1="12"
                x2="23"
                y2="12"
              />
              <line
                x1="4.22"
                y1="19.78"
                x2="5.64"
                y2="18.36"
              />
              <line
                x1="18.36"
                y1="5.64"
                x2="19.78"
                y2="4.22"
              />
            </g>
          </svg>
        </button>
        <DropdownMenu
          v-if="!isMobileSubdomain"
          :modal="false"
        >
          <DropdownMenuTrigger as-child>
            <ShadCnButton
              variant="default"
              size="icon"
              class="min-w-10 min-h-10 rounded-full bg-prim-col-foreground-2/50 hover:bg-prim-col-foreground-2/80"
            >
              <CircleUser class="h-5 w-5 text-white" />
              <span class="sr-only">Toggle user menu</span>
            </ShadCnButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>
              <div class="flex flex-col">
                <div>{{ authStore.user?.name }}</div>
                <div class="text-xs">
                  {{ authStore.user?.email }}
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem class="cursor-pointer bg-prim-col-foreground-2/20 hover:bg-prim-col-foreground-2/40 focus:bg-prim-col-foreground-2/40 focus:text-black dark:focus:text-white">
              <router-link
                :to="{ name: routeMap.settings.name }"
              >
                {{ $t('misc.settings') }}
              </router-link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              class="cursor-pointer bg-prim-col-foreground-2/40 hover:bg-prim-col-foreground-2/50 focus:bg-prim-col-foreground-2/50 focus:text-black dark:focus:text-white"
              @click="onLogoutPress"
            >
              {{ $t('login.log-out') }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  </header>
</template>

<style lang="css">
@import "open-props/easings";

.theme-toggle {
  --size: 1.65rem;
  --icon-fill: rgb(var(--prim-col-foreground-contrast));
  --icon-fill-hover: rgb(var(--prim-col-selected-1));
  background: none;
  border: none;
  padding: 0;
  inline-size: var(--size);
  block-size: var(--size);
  aspect-ratio: 1;
  border-radius: 50%;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  outline-offset: 5px;

  & > svg {
    inline-size: 100%;
    block-size: 100%;
    stroke-linecap: round;
  }
}

.dark .theme-toggle {
  --icon-fill: rgb(var(--prim-col-foreground-contrast));
  --icon-fill-hover: rgb(var(--prim-col-selected-1));
}

.sun-and-moon > :is(.moon, .sun, .sun-beams) {
  transform-origin: center;
}

.sun-and-moon > :is(.moon, .sun) {
  fill: var(--icon-fill);
}

.theme-toggle:is(:hover, :focus-visible) > .sun-and-moon > :is(.moon, .sun) {
  fill: var(--icon-fill-hover);
}

.sun-and-moon > .sun-beams {
  stroke: var(--icon-fill);
  stroke-width: 2px;
}

.theme-toggle:is(:hover, :focus-visible) .sun-and-moon > .sun-beams {
  stroke: var(--icon-fill-hover);
}

.dark .sun-and-moon > .sun {
  transform: scale(1.75);
}

.dark .sun-and-moon > .sun-beams {
  opacity: 0;
}

.dark .sun-and-moon > .moon > circle {
  transform: translateX(-7px);
}

@supports (cx: 1) {
  .dark .sun-and-moon > .moon > circle {
    cx: 17;
    transform: translateX(0);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .sun-and-moon > .sun {
    transition: transform .5s var(--ease-elastic-3);
  }

  .sun-and-moon > .sun-beams {
    transition: transform .5s var(--ease-elastic-4), opacity .5s var(--ease-3);
  }

  .sun-and-moon .moon > circle {
    transition: transform .25s var(--ease-out-5);
  }

  @supports (cx: 1) {
    .sun-and-moon .moon > circle {
      transition: cx .25s var(--ease-out-5);
    }
  }

  .dark .sun-and-moon > .sun {
    transition-timing-function: var(--ease-3);
    transition-duration: .25s;
    transform: scale(1.75);
  }

  .dark .sun-and-moon > .sun-beams {
    transition-duration: .15s;
    transform: rotateZ(-25deg);
  }

  .dark .sun-and-moon > .moon > circle {
    transition-duration: .5s;
    transition-delay: .25s;
  }
}

</style>
