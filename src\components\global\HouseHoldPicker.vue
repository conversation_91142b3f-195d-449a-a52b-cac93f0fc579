<script lang="ts" setup>
import { PlusIcon } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import CreateOrEditInstallationDialog from '@/pages/dashboard/CreateOrEditInstallationDialog.vue';
import { routeMap } from '@/router/routes';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from '@/shadcn-components/ui/navigation-menu';
import { useInstallationStore } from '@/stores/installation-store';
import PageLoader from '@/components/global/PageLoader.vue';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import type { InstallationDetailData } from '@/pages/installation/types/installation-types';

const route = useRoute();
const installationStore = useInstallationStore();
const showCreateInstallationButton = ref(false);
const installationCreateDialogOpened = ref(false);
const currentInstallation = computed(() => installationStore.installations.find(v => v.id === route.params.installationId));
const availableInstallations = computed(() => installationStore.installations);
let timeout: ReturnType<typeof setTimeout>;
const router = useRouter();

const onMouseEnterSelect = () => {
  clearTimeout(timeout);
  showCreateInstallationButton.value = true;
};

const onMouseLeaveSelect = () => {
  timeout = setTimeout(() => {
    showCreateInstallationButton.value = false;
  }, 1000);
};

const onInstallationCreated = async(installationData: InstallationDetailData) => {
  await router.replace({ name: routeMap.home.children.installation.name, params: { installationId: installationData.id } });
};
</script>

<template>
  <div
    class="relative"
    @mouseenter="onMouseEnterSelect"
    @mouseleave="onMouseLeaveSelect"
  >
    <NavigationMenu v-if="installationStore.installations.length > 0">
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger class="cursor-pointer">
            <div class="flex flex-col items-start pr-1 sm:max-w-full" :class="isMobileSubdomain ? 'max-w-40' : 'max-w-12'">
              <div class="text-[0.7rem] -mb-1">
                {{ $t('installation.title-base') }}
              </div>
              <div class="break-all line-clamp-1">
                {{ currentInstallation?.title }}
              </div>
            </div>
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul class="grid w-[400px] gap-1 p-2 max-h-[80vh] overflow-y-auto">
              <li
                v-for="(menuItem, idx) in availableInstallations"
                :key="idx"
              >
                <NavigationMenuLink as-child>
                  <router-link
                    :to="{name: routeMap.home.children.installation.name, params: {installationId: menuItem.id}}"
                    class="block cursor-pointer select-none space-y-1 rounded-md p-3 leading-none no-underline outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                  >
                    <div class="flex items-center gap-2">
                      <div class="text-sm font-medium leading-none">
                        {{ menuItem.title }}
                      </div>
                      <div
                        v-if="!menuItem.is_owner"
                        class="text-xs bg-yellow-500 text-black w-fit px-1 py-0.5 rounded-md relative bottom-px"
                      >
                        {{ $t('installation.shared-installation') }}
                      </div>
                    </div>
                  </router-link>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
    <transition appear>
      <div
        v-if="showCreateInstallationButton || isMobileSubdomain"
        class="absolute cursor-pointer -right-7 top-[3px] translate-x-1/2 w-10 h-[38px] rounded-md bg-background flex items-center justify-center"
        :title="$t('installation.create-installation')"
        @click="installationCreateDialogOpened = true;"
      >
        <PlusIcon class="w-5 h-5" />
      </div>
    </transition>
    <suspense>
      <CreateOrEditInstallationDialog
        v-model="installationCreateDialogOpened"
        @installation-created="onInstallationCreated"
      />
      <template #fallback>
        <teleport to="body">
          <div class="fixed w-screen h-dvh bg-black/60 z-50 top-0 left-0">
            <PageLoader :absolute-center="true" />
          </div>
        </teleport>
      </template>
    </suspense>
  </div>
</template>
