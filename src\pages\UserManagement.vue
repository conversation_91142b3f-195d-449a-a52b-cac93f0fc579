<script lang="ts" setup>
import { PlusIcon } from 'lucide-vue-next';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import PageLoader from '@/components/global/PageLoader.vue';
import CreateUserDialog from '@/pages/user-management/CreateUserDialog.vue';
import UsersTable from '@/pages/user-management/UsersTable.vue';
import { routeMap } from '@/router/routes';
import { customAxios } from '@/util/axios';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import { cloneObject } from '@/util/objects';
import type { ApiPermissionsResponse, ApiRolesResponse, ApiUsersResponse } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';
import type { UserManagementData } from '@/util/types/user-management';

const route = useRoute();
initializeBasicBreadcrumbBehaviour(routeMap.management.children.users.meta.i18nTitle, routeMap.management.children.users.name, true, route);

const componentState = ref(ComponentStateType.LOADING);
const formerUsersData = ref<UserManagementData>([]);
const usersDataChanges = ref<UserManagementData>([]);
const pagingData = ref<Pick<ApiUsersResponse, 'links' | 'meta'>>();
const allPermissions = ref<PermissionData[]>([]);
const allRoles = ref<RoleData[]>([]);
const creatingNewUserModalShown = ref(false);

const reFetchData = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    const usersResponse = await customAxios.get<ApiUsersResponse>('/users');
    const cleanData = usersResponse.data.data.map(v => ({
      ...v,
      roles: v.roles.map(role => role.id),
    }));
    formerUsersData.value = cloneObject(cleanData);
    usersDataChanges.value = cleanData;
    pagingData.value = {
      links: usersResponse.data.links,
      meta: usersResponse.data.meta,
    };
    const { data: permissionsResponse } = await customAxios.get<ApiPermissionsResponse>('/permissions');
    allPermissions.value = permissionsResponse.data;
    const { data: rolesResponse } = await customAxios.get<ApiRolesResponse>('/roles');
    allRoles.value = rolesResponse.data;
    componentState.value = ComponentStateType.OK;
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

await reFetchData();

</script>

<template>
  <div class="relative grid gap-2">
    <div
      v-if="componentState !== ComponentStateType.ERROR"
      class="flex items-center justify-between"
    >
      <div
        class="w-fit bg-prim-col-foreground-2 hover:bg-prim-col-foreground-2/50 dark:bg-prim-col-foreground-1 dark:hover:bg-prim-col-foreground-2 rounded-lg p-2 transition-colors flex items-center cursor-pointer"
        @click="creatingNewUserModalShown = true;"
      >
        <PlusIcon class="aspect-square text-white" />
      </div>
    </div>
    <UsersTable
      v-if="usersDataChanges.length > 0"
      v-model="usersDataChanges"
      :former-user-data="formerUsersData"
      :all-permissions="allPermissions"
      :all-roles="allRoles"
      @user-deleted="reFetchData"
      @user-updated="reFetchData"
    />
    <transition appear>
      <div
        v-if="componentState === ComponentStateType.LOADING"
        class="absolute left-0 top-0 w-full h-full bg-white/70 flex items-center justify-center"
      >
        <PageLoader :flex-center="true" />
      </div>
    </transition>
    <div
      v-if="componentState === ComponentStateType.ERROR"
      class="absolute left-0 top-0 w-full min-h-[10rem] bg-red-200 h-full bg-white/70 flex items-center justify-center"
    >
      <span class="font-medium">{{ $t('misc.failed-to-get-data') }}</span>
    </div>
    <CreateUserDialog
      v-model="creatingNewUserModalShown"
      :all-roles="allRoles"
      @user-created="reFetchData"
    />
  </div>
</template>

