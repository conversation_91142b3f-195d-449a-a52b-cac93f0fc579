<script setup lang="ts">
import { SVG, type Rect, type Circle, type Svg, type Path } from '@svgdotjs/svg.js';
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { isLightModeEnabled } from '@/composables/theme';

// Define the type for schemeData
interface SchemeData {
  pathId: string;
  value: number;
  dots?: number;
  dynamicInputId?: string;
  dynamicInputMetric?: string;
  oppositeSvgPath?: boolean; // New property to control path direction
}

// Define a type for the scheme's data structure
interface Scheme {
  solarPanel: SchemeData;
  plant: SchemeData;
  battery: SchemeData;
  house: SchemeData;
}

interface Props {
  batteryState: number,
}

const props = defineProps<Props>();
const schemeContainer = ref<HTMLDivElement | null>(null);
const solarScheme = ref<SVGSVGElement | null>(null);
const svgJsEl = ref<Svg>();

// Define reactive schemeData structure
const schemeData: Scheme = reactive<Scheme>({
  solarPanel: {
    pathId: 'solar-central',
    value: Math.floor(Math.random() * 1001),
    dots: 10,
    dynamicInputId: 'dynamic-val-panel',
    dynamicInputMetric: 'W',
    oppositeSvgPath: true,
  },
  plant: {
    pathId: 'plant-central',
    value: Math.floor(Math.random() * 2001) - 1000,
    dots: 10,
    dynamicInputId: 'dynamic-val-plant',
    dynamicInputMetric: 'W',
    oppositeSvgPath: false,
  },
  battery: {
    pathId: 'battery-central',
    value: props.batteryState,
    dots: 3,
    dynamicInputId: 'dynamic-val-battery',
    dynamicInputMetric: '%',
    oppositeSvgPath: true, // This path starts from the center, so dots move in reverse
  },
  house: {
    pathId: 'house-central',
    value: Math.floor(Math.random() * 1001),
    dots: 10,
    dynamicInputId: 'dynamic-val-house',
    dynamicInputMetric: 'W',
    oppositeSvgPath: true,
  },
});

const getSvgPaths = () => ({
  solarPanel: svgJsEl.value!.findOne(`#${schemeData.solarPanel.pathId}`) as Path,
  plant: svgJsEl.value!.findOne(`#${schemeData.plant.pathId}`) as Path,
  battery: svgJsEl.value!.findOne(`#${schemeData.battery.pathId}`) as Path,
  house: svgJsEl.value!.findOne(`#${schemeData.house.pathId}`) as Path,
});

// Function to insert the dynamic value and metric inside the corresponding rectangles
const updateDynamicInputs = (svg: Svg): void => {
  // Clear existing text from dynamic inputs
  svg.find('text').forEach(text => text.remove()); // Remove all text elements

  (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
    const data = schemeData[key];
    const valueText = `${data.value} ${data.dynamicInputMetric}`; // Combine value and metric

    // Find the rectangle by dynamicInputId
    const dynamicInput = svg.findOne(`#${data.dynamicInputId}`) as unknown as Rect;

    if (dynamicInput) {
      // Add text inside the rectangle for value + metric
      const bbox = dynamicInput.bbox();
      svg
        .text(valueText)
        .font({ size: 20, family: 'Roboto', anchor: 'middle' })
        .center(bbox.cx, bbox.cy); // Center the text inside the rectangle
    }
  });
};

const animationFrameIds: Record<string, number | null> = {
  solarPanel: null,
  plant: null,
  battery: null,
  house: null,
};

const animateDotsOnPath = (
  path: Path,
  color: string,
  value: number,
  dotCount: number = 5,
  oppositeSvgPath: boolean = false,
): void => {
  if (!path || value === 0) {
    return;
  }

  const length = path.length();
  const spacing = length / dotCount;

  // Select or create dots
  const dots = svgJsEl.value!.find(`circle.dot-${path.id()}`) as unknown as Circle[];

  if (dots.length < dotCount) {
    for (let i = dots.length; i < dotCount; i++) {
      const dot = (path.parent()! as Svg)
        .circle(10)
        .fill(color)
        .addClass(`dot-${path.id()}`);
      dots.push(dot);
    }
  }

  // Stop any existing animation for this path
  if (animationFrameIds[path.id()]) {
    cancelAnimationFrame(animationFrameIds[path.id()]!);
  }

  // Track the start time
  const start = performance.now();
  const speed = 4000; // Animation duration in ms

  const animateAllDots = (time: number) => {
    const elapsed = (time - start) % speed;

    dots.forEach((dot, index) => {
      const pos = (elapsed / speed + index * (spacing / length)) % 1;
      let point;

      // Re-evaluate direction during each frame
      const isReverseDirection =
        (value < 0 && !oppositeSvgPath) || (value > 0 && oppositeSvgPath);

      if (isReverseDirection) {
        point = path.pointAt(length - pos * length); // Reverse direction
      } else {
        point = path.pointAt(pos * length); // Forward direction
      }

      if (point) {
        dot.center(point.x, point.y);
      }
    });

    // Store the current animation frame ID
    animationFrameIds[path.id()] = requestAnimationFrame(animateAllDots);
  };

  // Start the new animation
  animationFrameIds[path.id()] = requestAnimationFrame(animateAllDots);
};

const initializeAndAnimatePaths = (): void => {
  const dotColor: string = `rgb(${getComputedStyle(document.body)
    .getPropertyValue('--prim-col-foreground-1')
    .replaceAll(' ', ',')})`;

  if (!solarScheme.value) {
    return;
  }

  svgJsEl.value = SVG(solarScheme.value); // Select the SVG element

  // Update text inputs for dynamic values
  updateDynamicInputs(svgJsEl.value);

  // Paths to animate
  const paths: Record<string, Path | null> = getSvgPaths();

  // Animate the dots for each path
  (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
    const path = paths[key];
    const value = schemeData[key].value;
    const oppositeSvgPath = schemeData[key].oppositeSvgPath;

    if (path) {
      // You can adjust the number of dots here for different paths
      animateDotsOnPath(path, dotColor, value, schemeData[key].dots, oppositeSvgPath);
    }
  });
};

const clearAnimations = () => {
  const paths: Record<string, Path | null> = getSvgPaths();
  (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
    const path = paths[key]!;

    svgJsEl.value!.find(`circle.dot-${path.id()}`).forEach(dot => dot.remove());
    if (animationFrameIds[path.id()]) {
      cancelAnimationFrame(animationFrameIds[path.id()]!); // Stop any ongoing animation
      animationFrameIds[path.id()] = null;
    }
  });
};

watch(isLightModeEnabled, () => {
  clearAnimations();
  initializeAndAnimatePaths(); // Reinitialize the animation on data change
});

watch(() => props.batteryState, () => {
  schemeData.battery.value = props.batteryState;
  schemeData.solarPanel.value = Math.floor(Math.random() * 1001);
  schemeData.plant.value = Math.floor(Math.random() * 2001) - 1000;
  schemeData.house.value = Math.floor(Math.random() * 1001);

  // Define the dot color
  const dotColor = `rgb(${getComputedStyle(document.body)
    .getPropertyValue('--prim-col-foreground-1')
    .replaceAll(' ', ',')})`;

  // Check if the SVG element is initialized
  if (solarScheme.value) {
    // Get all the paths
    const paths: Record<string, Path | null> = getSvgPaths();

    // Iterate through each path and update the dots accordingly
    (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
      const path = paths[key];
      const value = schemeData[key].value;
      const oppositeSvgPath = schemeData[key].oppositeSvgPath;

      if (path) {
        // If the value is 0, skip animating the dots
        if (value !== 0) {
          animateDotsOnPath(path, dotColor, value, schemeData[key].dots, oppositeSvgPath);
        } else {
          // If the value is 0, remove the dots to stop animation
          svgJsEl.value!.find(`circle.dot-${path.id()}`).forEach(dot => dot.remove());
          if (animationFrameIds[path.id()]) {
            cancelAnimationFrame(animationFrameIds[path.id()]!); // Stop any ongoing animation
            animationFrameIds[path.id()] = null;
          }
        }
      }
    });

    // Optionally, update the dynamic input values (if necessary)
    updateDynamicInputs(svgJsEl.value!);
  }
});

onMounted((): void => {
  initializeAndAnimatePaths();
});

onBeforeUnmount(() => {
  clearAnimations();
});
</script>

<template>
  <div
    id="solar-scheme-container"
    ref="schemeContainer"
    class="flex-1 relative"
  >
    <!-- eslint-disable no-mixed-spaces-and-tabs -->
    <svg
      id="solar-scheme-01"
      ref="solarScheme"
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      viewBox="0 0 1920 450.9"
      xml:space="preserve"
    >
      <g id="lines">
        <path
          id="solar-central"
          fill="none"
          stroke="#000000"
          stroke-width="4.25"
          stroke-miterlimit="10"
          d="M923.2,161.4H725.4l0,0
		c-12.1,0-21.9-9.8-21.9-21.9s-9.8-21.9-21.9-21.9l0,0l-198.9-0.2"
        />
        <path
          id="battery-central"
          fill="none"
          stroke="#000000"
          stroke-width="4.25"
          stroke-miterlimit="10"
          d="M960.0379639,198.6162109
		v114.5000305"
        />
        <path
          id="house-central"
          fill="none"
          stroke="#000000"
          stroke-width="4.25"
          stroke-miterlimit="10"
          d="M1423.8,232.8l-189.7,0
		c-13.3,0-24.2-10.8-24.2-24.2c0-13.3-10.8-24.2-24.2-24.2l0,0l-187.9,0"
        />
        <path
          id="plant-central"
          fill="none"
          stroke="#000000"
          stroke-width="4.25"
          stroke-miterlimit="10"
          d="M481.4,334.7h201.2l0,0
		c11.2,0,20.3-9.1,20.3-20.3l0,0V204.8l0,0c0-11.2,9.1-20.3,20.3-20.3h0l198.9,0"
        />
      </g>
      <g id="nodes">
        <g id="solar-panel-node">
          <path
            class="node-background"
            fill="#EAEAEA"
            d="M418.7,191.9h-26.3c-35.8,0-64.8-29-64.8-64.8v-26.3c0-35.8,29-64.8,64.8-64.8h26.3
			c35.8,0,64.8,29,64.8,64.8V127C483.5,162.8,454.5,191.9,418.7,191.9z"
          />
          <g>
            <path
              d="M440.4,88.3c-6,0-10.9-4.9-10.9-10.9c0-6,4.9-10.9,10.9-10.9s10.9,4.9,10.9,10.9C451.3,83.4,446.4,88.3,440.4,88.3z
				 M440.4,68.5c-4.9,0-8.8,4-8.8,8.8c0,4.9,4,8.8,8.8,8.8c4.9,0,8.8-4,8.8-8.8C449.2,72.5,445.3,68.5,440.4,68.5z M412,96.6
				l16.6-10.8c0.2-0.2,0.3-0.5,0.1-0.7c-0.2-0.2-0.5-0.3-0.7-0.1l-16.6,10.8c-0.2,0.2-0.3,0.5-0.1,0.7c0.1,0.1,0.3,0.2,0.4,0.2
				C411.8,96.7,411.9,96.7,412,96.6z M429,69c0.2-0.2,0.1-0.5-0.1-0.7l-2.3-1.8c-0.2-0.2-0.5-0.1-0.7,0.1c-0.2,0.2-0.1,0.5,0.1,0.7
				l2.3,1.8c0.1,0.1,0.2,0.1,0.3,0.1C428.7,69.2,428.9,69.2,429,69z M435.9,64.2c0.3-0.1,0.4-0.4,0.3-0.6l-0.9-2.8
				c-0.1-0.3-0.4-0.4-0.6-0.3c-0.3,0.1-0.4,0.4-0.3,0.6l0.9,2.8c0.1,0.2,0.3,0.4,0.5,0.4C435.8,64.2,435.8,64.2,435.9,64.2z
				 M445,63.9l0.9-2.8c0.1-0.3-0.1-0.6-0.3-0.6c-0.3-0.1-0.6,0.1-0.6,0.3l-0.9,2.8c-0.1,0.3,0.1,0.6,0.3,0.6c0.1,0,0.1,0,0.2,0
				C444.7,64.3,444.9,64.1,445,63.9z M451.8,69.4l2.4-1.7c0.2-0.2,0.3-0.5,0.1-0.7c-0.2-0.2-0.5-0.3-0.7-0.1l-2.4,1.7
				c-0.2,0.2-0.3,0.5-0.1,0.7c0.1,0.1,0.3,0.2,0.4,0.2C451.6,69.5,451.7,69.5,451.8,69.4z M457.5,77.4c0-0.3-0.2-0.5-0.5-0.5l-2.9,0
				c0,0,0,0,0,0c-0.3,0-0.5,0.2-0.5,0.5c0,0.3,0.2,0.5,0.5,0.5l2.9,0c0,0,0,0,0,0C457.3,77.9,457.5,77.7,457.5,77.4z M454,87.7
				c0.2-0.2,0.1-0.5-0.1-0.7l-2.3-1.8c-0.2-0.2-0.5-0.1-0.7,0.1c-0.2,0.2-0.1,0.5,0.1,0.7l2.3,1.8c0.1,0.1,0.2,0.1,0.3,0.1
				C453.8,87.9,453.9,87.9,454,87.7z M454,129.6c0.3-0.1,0.4-0.3,0.4-0.6l-9.7-38.4c-0.1-0.3-0.3-0.4-0.6-0.4
				c-0.3,0.1-0.4,0.3-0.4,0.6l9.7,38.4c0.1,0.2,0.3,0.4,0.5,0.4C453.9,129.6,454,129.6,454,129.6z M425.1,116.5l10.8-25.7
				c0.1-0.3,0-0.6-0.3-0.7c-0.3-0.1-0.6,0-0.7,0.3l-10.8,25.6c-0.1,0.3,0,0.6,0.3,0.7c0.1,0,0.1,0,0.2,0
				C424.8,116.8,425,116.7,425.1,116.5z M368.2,81.1l57.6-3.6c0.3,0,0.5-0.3,0.5-0.5c0-0.3-0.2-0.5-0.5-0.5l-57.6,3.6
				c-0.3,0-0.5,0.3-0.5,0.5C367.6,80.9,367.9,81.1,368.2,81.1C368.1,81.1,368.1,81.1,368.2,81.1z M429,69c0.2-0.2,0.1-0.5-0.1-0.7
				l-2.3-1.8c-0.2-0.2-0.5-0.1-0.7,0.1c-0.2,0.2-0.1,0.5,0.1,0.7l2.3,1.8c0.1,0.1,0.2,0.1,0.3,0.1C428.7,69.2,428.9,69.2,429,69z
				 M435.9,64.2c0.3-0.1,0.4-0.4,0.3-0.6l-0.9-2.8c-0.1-0.3-0.4-0.4-0.6-0.3c-0.3,0.1-0.4,0.4-0.3,0.6l0.9,2.8
				c0.1,0.2,0.3,0.4,0.5,0.4C435.8,64.2,435.8,64.2,435.9,64.2z M445,63.9l0.9-2.8c0.1-0.3-0.1-0.6-0.3-0.6
				c-0.3-0.1-0.6,0.1-0.6,0.3l-0.9,2.8c-0.1,0.3,0.1,0.6,0.3,0.6c0.1,0,0.1,0,0.2,0C444.7,64.3,444.9,64.2,445,63.9z M451.8,69.4
				l2.4-1.7c0.2-0.2,0.3-0.5,0.1-0.7c-0.2-0.2-0.5-0.3-0.7-0.1l-2.4,1.7c-0.2,0.2-0.3,0.5-0.1,0.7c0.1,0.1,0.3,0.2,0.4,0.2
				C451.6,69.5,451.7,69.5,451.8,69.4z M429.9,103c-0.2-0.6-0.8-0.9-1.4-0.9l-70.8-7.7c0,0,0,0,0,0c-0.1,0-0.2,0-0.3,0
				c0,0-0.1,0-0.1,0c-0.1,0-0.2,0.1-0.2,0.1c0,0-0.1,0-0.1,0.1c0,0-0.1,0-0.1,0.1l-2.6,1.9c-0.6,0.4-0.8,1.2-0.5,1.9l22.5,53.7
				c0.2,0.6,0.8,0.9,1.4,0.9h4.1h0.6h26l1.5,5.1l-11.2,7.5c-0.4,0.3-0.6,0.8-0.4,1.2c0.2,0.4,0.5,0.7,1,0.7c0,0,0.1,0,0.1,0
				l22.4-3.2l16.7-3.4c0.5-0.1,0.9-0.6,0.8-1.1c0-0.5-0.5-0.9-1-0.9l-15.7-0.7v-5H449c0,0,0,0,0,0c0.8,0,1.5-0.7,1.5-1.5
				c0-0.3-0.1-0.6-0.2-0.8L429.9,103z M443,141.4l-12.5-0.5l-2.3-1.6L426,134l1.2-1.5l12.3,0.6L443,141.4z M413.2,140.3l-2.1-1.5
				l-2.4-5.7l1.2-1.5l12.4,0.6l2.5,1.6l2.3,5.5l-1.3,1.4L413.2,140.3z M421.9,131.2l-12.5-0.6l-2.5-1.7l-2.5-6l1.2-1.6l12.6,0.8
				l2.3,1.5l2.7,6.3L421.9,131.2z M417.5,121l-12.7-0.8l-2.3-1.6l-2.6-6.2l1.1-1.3l13.1,1l2.2,1.4l2.6,6.1L417.5,121z M401.5,118.6
				l-1.2,1.2l-14.1-0.9l-2.4-1.7l-2.7-6.4l1.1-1.3l14.6,1.1l1.9,1.2L401.5,118.6z M386.8,120l13.8,0.9l2.5,1.6l2.7,6.4l-1.2,1.3
				l-14.1-0.7l-2-1.4l-2.8-6.7L386.8,120z M391.4,130.7l13.9,0.7l2.2,1.4l2.6,6.2l-1.1,1.1l-14.1-0.5l-2.2-1.5l-2.5-5.9L391.4,130.7
				z M439,132l-12.6-0.6l-2.2-1.5l-2.6-6.2l1.1-1.4l12.5,0.8L439,132z M434.8,122.1l-12.4-0.8l-2.5-1.7l-2.5-5.8l1.1-1.4l12.5,1
				L434.8,122.1z M427.5,105.1l3.1,7.2l-12.3-1l-2.7-1.9l-2.5-5.9L427.5,105.1z M412,103.5l2.6,6.2l-1.3,1.3l-12.3-1l-3-2.1
				l-2.6-6.3L412,103.5z M394.2,101.6l2.8,6.6l-1.3,1.4l-14-1.1l-2.3-1.6l-3.1-7.2L394.2,101.6z M360.3,97.9l14.9,1.6l3.1,7.2
				l-1.3,1.4l-12.8-1L360.3,97.9z M364.6,108.2l12.8,1l2.6,1.7l2.8,6.5l-1.2,1.3l-12.9-0.8L364.6,108.2z M369.1,118.9l12.9,0.8
				l2.4,1.5l2.9,6.8l-1.2,1.3l-12.8-0.6L369.1,118.9z M373.7,129.8l12.6,0.6l2.7,1.8l2.6,6.1l-1.2,1.2l-12.8-0.5L373.7,129.8z
				 M382.2,149.9l-4.2-9.9l12.8,0.5l2.4,1.6l3.3,7.9L382.2,149.9L382.2,149.9z M397.6,149.9l-3.2-7.5l1.3-1.7l13.4,0.5l2.5,1.6
				l3,7.1H397.6z M403.4,164.7l7.7-5.1l6.8,3L403.4,164.7z M420.4,161.6l-4.1-1.9l4.1,0.2V161.6z M420.4,157.9l-8.7-0.4l-1.3-4.5h10
				L420.4,157.9L420.4,157.9z M430,160.4l-7.5,1.5V160L430,160.4z M415.8,149.9l-2.9-6.8l1.4-1.8l12.2,0.4l2.3,1.5l2.8,6.6H415.8
				L415.8,149.9z M432.7,149.9l-2.7-6.4l1.2-1.6l12.2,0.4l3.2,7.5H432.7z"
            />
          </g>
        </g>
        <g id="battery-node">
          <path
            class="node-background"
            fill="#EAEAEA"
            d="M977.8,414.9h-35.7c-20.4,0-36.9-16.5-36.9-36.9v-27.9c0-20.4,16.5-36.9,36.9-36.9
			h35.7c20.4,0,36.9,16.5,36.9,36.9V378C1014.7,398.4,998.2,414.9,977.8,414.9z"
          />
          <g id="battery-object">
            <path
              d="M989.4,344.3h-4.5v-4.5c0-2.5-2-4.5-4.5-4.5l-47.5,0c-2.5,0-4.5,2-4.5,4.5v22.6c0,2.5,2,4.5,4.5,4.5h47.5
				c2.5,0,4.5-2,4.5-4.5v-4.5h4.5c1.3,0,2.3-1,2.3-2.3l0-9.1C991.7,345.3,990.7,344.3,989.4,344.3L989.4,344.3z M980.4,362.4h-47.5
				v-22.6h47.5V362.4z"
            />
            <path
              v-if="batteryState > 19"
              id="battery-20"
              d="M935.1,359v-15.8c0-0.6,0.5-1.1,1.1-1.1h4.5c0.6,0,1.1,0.5,1.1,1.1V359c0,0.6-0.5,1.1-1.1,1.1h-4.5
				C935.6,360.1,935.1,359.6,935.1,359L935.1,359z"
            />
            <path
              v-show="batteryState > 39"
              id="battery-40"
              d="M944.2,359v-15.8c0-0.6,0.5-1.1,1.1-1.1h4.5c0.6,0,1.1,0.5,1.1,1.1V359c0,0.6-0.5,1.1-1.1,1.1h-4.5
				C944.7,360.1,944.2,359.6,944.2,359L944.2,359z"
            />
            <path
              v-show="batteryState > 59"
              id="battery-60"
              d="M953.2,359v-15.8c0-0.6,0.5-1.1,1.1-1.1h4.5c0.6,0,1.1,0.5,1.1,1.1V359c0,0.6-0.5,1.1-1.1,1.1h-4.5
				C953.7,360.1,953.2,359.6,953.2,359L953.2,359z"
            />
            <path
              v-show="batteryState > 79"
              id="battery-80"
              d="M962.3,359v-15.8c0-0.6,0.5-1.1,1.1-1.1h4.5c0.6,0,1.1,0.5,1.1,1.1V359c0,0.6-0.5,1.1-1.1,1.1h-4.5
				C962.8,360.1,962.3,359.6,962.3,359L962.3,359z"
            />
            <path
              v-show="batteryState > 99"
              id="battery-100"
              d="M971.3,359v-15.8c0-0.6,0.5-1.1,1.1-1.1h4.5c0.6,0,1.1,0.5,1.1,1.1V359c0,0.6-0.5,1.1-1.1,1.1h-4.5
				C971.8,360.1,971.3,359.6,971.3,359L971.3,359z"
            />
          </g>
        </g>
        <g id="house-node">
          <path
            class="node-background"
            fill="#EAEAEA"
            d="M1514.2,310.6h-25.6c-35.8,0-64.8-29-64.8-64.8v-25.6c0-35.8,29-64.8,64.8-64.8h25.6
			c35.8,0,64.8,29,64.8,64.8v25.6C1579,281.6,1550,310.6,1514.2,310.6z"
          />
          <g>
            <path
              d="M1490.8,283.3c0,1.8,0,3.5,0,5.1c-10.4,0-20.8,0-31.3,0c0-16.9,0-33.7,0-50.8c-1.3,1.1-2.4,2-3.5,3.1
				c-1.1-1.3-2.2-2.5-3.4-3.9c16.3-14.5,32.5-28.9,48.9-43.4c16.3,14.5,32.6,28.9,48.9,43.4c-1.1,1.3-2.2,2.5-3.4,3.9
				c-1.1-1-2.2-1.9-3.5-3.1c0,17.1,0,33.9,0,50.8c-14.9,0-29.7,0-44.5,0c0-7,0-14,0-20.8c-0.3-0.4-0.6-0.4-0.9-0.5
				c-7-1.6-12-7.7-12.2-14.9c-0.1-3.6,0-7.1,0-10.8c1.7,0,3.4,0,5.2,0c0.1-4.4,0-8.7,0.1-13c1.7,0,3.4,0,5.2,0c0,4.3,0,8.5,0,12.9
				c3.6,0,7,0,10.5,0c0-4.3,0-8.5,0-12.9c0.9-0.2,1.8-0.1,2.6-0.1c0.8,0,1.6,0,2.6,0c0.1,4.3,0,8.6,0.1,13c1.7,0,3.4,0,4.9,0
				c0.4,0.4,0.3,0.8,0.3,1.2c0,3,0,6,0,9c0.1,8.3-5.8,14.4-12.6,15.7c-0.1,0-0.2,0.1-0.3,0.1c-0.3,0.9-0.3,14.7,0,15.9
				c11.2,0,22.5,0,33.8,0c0-16.8,0-33.5,0-50.4c-12.1-10.8-24.3-21.6-36.6-32.5c-12.2,10.8-24.4,21.7-36.6,32.5
				c0,16.8,0,33.5,0,50.4C1473.5,283.3,1482.1,283.3,1490.8,283.3z M1491.1,246.6c-0.2,0.3-0.2,0.6-0.2,0.9c0.1,1.9-0.1,3.8,0.1,5.8
				c0.7,4.7,4.6,8.5,9.2,9c5.5,0.5,9.8-2.9,11.2-7.3c0.8-2.7,0.4-5.5,0.5-8.3C1504.9,246.6,1498,246.6,1491.1,246.6z"
            />
            <path
              d="M1448.2,232.8c-1.2-1.3-2.3-2.5-3.5-3.8c18.9-17.1,37.8-34.1,56.8-51.3c18.9,17.1,37.8,34.2,56.8,51.3
				c-1.2,1.3-2.3,2.5-3.5,3.8c-17.8-16-35.5-32-53.3-48.1C1483.7,200.7,1466,216.7,1448.2,232.8z"
            />
          </g>
        </g>
        <g id="plant-node">
          <path
            class="node-background"
            fill="#EAEAEA"
            d="M416.6,407.8h-26.3c-35.8,0-64.8-29-64.8-64.8v-26.3c0-35.8,29-64.8,64.8-64.8h26.3
			c35.8,0,64.8,29,64.8,64.8V343C481.4,378.8,452.4,407.8,416.6,407.8z"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M359.4,309.9l-1.1,10.8h12.1l-1.1-10.8H359.4z M351.4,336.8l5.1,0.6l0.8-7.3h14
			l1,9.2l33.1,3.9l3.9-10.1v-10.5h-8.8v2.5c0,0.7-0.6,1.3-1.3,1.3c-0.7,0-1.3-0.6-1.3-1.3v-2.5h-1.4v2.5c0,0.7-0.6,1.3-1.3,1.3
			c-0.7,0-1.3-0.6-1.3-1.3v-2.5h-1c-0.6,0-1-0.5-1-1c0-0.4,0.2-0.8,0.6-1l16.7-7.5v-4.8h-8.8v2.4c0,0.7-0.6,1.3-1.3,1.3
			c-0.7,0-1.3-0.6-1.3-1.3v-2.4h-1.4v2.4c0,0.7-0.6,1.3-1.3,1.3c-0.7,0-1.3-0.6-1.3-1.3v-2.4h-1c-0.6,0-1-0.5-1-1
			c0-0.4,0.2-0.8,0.6-0.9l16.9-7.8l0,0l9.1-11.6c0.3-0.5,1-0.5,1.4-0.2c0,0,0,0,0,0c0.1,0.1,0.2,0.1,0.2,0.2l8.8,11.6l0.1,0.1
			l16.8,7.7c0.4,0.2,0.7,0.5,0.7,1c0,0.5-0.5,1-1,1h-1.8v2.4c0,0.7-0.6,1.3-1.3,1.3c-0.7,0-1.3-0.6-1.3-1.3v-2.4h-1.3v2.4
			c0,0.7-0.6,1.3-1.3,1.3c-0.7,0-1.3-0.6-1.3-1.3c0,0,0,0,0,0v-2.4h-8.1v4.8l16.6,7.4c0.5,0.2,0.8,0.8,0.7,1.3
			c-0.1,0.4-0.5,0.7-1,0.7h-1.8v2.5c0,0.7-0.6,1.3-1.3,1.3c-0.7,0-1.3-0.6-1.3-1.3v-2.5h-1.3v2.5c0,0.7-0.6,1.3-1.3,1.3
			c-0.7,0-1.3-0.6-1.3-1.3v-2.5h-8.1V333l6.1,13.6l12.5,1.5v22.4h-96.6V336.8z M370.1,307.1c0.3,0.6,0,1.3-0.6,1.6
			c-0.6,0.3-1.3,0-1.6-0.6l0,0c-0.3-0.7-0.4-1.5-0.4-2.2c0.1-0.7,0.3-1.3,0.6-1.9c0.3-0.6,1-0.8,1.6-0.5c0,0,0,0,0,0
			c0.1,0.1,0.2,0.1,0.3,0.2c0.8,0.7,1.7,1.2,2.7,1.5c0.5,0.1,1,0.1,1.4-0.1c0.4-0.2,0.7-0.5,0.8-0.9c0.3-0.8,0.4-1.6,0.3-2.5
			c-0.1-0.7,0.4-1.2,1.1-1.3c0.5-0.1,1,0.2,1.2,0.7c0.8,1.4,2.2,2.3,3.8,2.5c0.8,0.1,1.5,0.1,2.3-0.1c0.6-0.2,1.2-0.5,1.7-1
			c0.8-1,1.1-2.4,0.7-3.7c-0.1-0.6,0.3-1.3,0.9-1.4c0.1,0,0.3,0,0.4,0c1.8,0.2,3.6-0.7,4.4-2.3c0.3-0.6,0.5-1.2,0.5-1.9
			c0-0.6-0.1-1.2-0.4-1.7c-0.6-1-1.9-1.6-3.9-1.4c-0.6,0.1-1.1-0.3-1.3-0.8c-0.3-1.3-1.1-2.4-2.3-3c-0.5-0.2-1.1-0.3-1.7-0.3
			c-0.6,0.1-1.1,0.3-1.6,0.7c-1,0.8-1.6,1.9-1.6,3.1c0,0.7-0.6,1.1-1.3,1.1c-0.5,0-0.9-0.4-1.1-0.9c-0.3-1-0.8-2-1.6-2.7
			c-0.6-0.5-1.3-0.7-2-0.7c-0.8,0-1.5,0.3-2.2,0.7c-0.9,0.6-1.6,1.4-2,2.4c-0.2,0.6-0.9,0.9-1.5,0.7c-0.1-0.1-0.3-0.1-0.4-0.2
			c-0.9-0.9-2-1.5-3.3-1.6c-0.6,0-1.1,0.1-1.6,0.4c-0.5,0.3-0.8,0.7-1,1.2c-0.4,1.3-0.4,2.7,0.1,3.9c0.2,0.6-0.1,1.3-0.7,1.5
			c0,0,0,0,0,0h-0.1c-1.3,0.3-2.3,1.2-2.6,2.5c-0.1,0.5-0.1,1,0,1.5c0.1,0.5,0.3,0.9,0.6,1.3c0.7,0.9,2,1.5,3.9,1.2
			c0.6-0.1,1.3,0.3,1.4,1c0.1,0.3,0,0.7-0.2,0.9c-0.4,0.6-0.7,1.2-0.9,1.8c-0.2,0.6-0.3,1.2-0.3,1.8c0.1,0.6-0.3,1.3-0.9,1.4
			c-0.6,0.1-1.3-0.3-1.4-0.9c0-0.2,0-0.3,0-0.5c0-0.8,0.2-1.7,0.4-2.5l0.2-0.5c-1.6-0.1-3.1-0.9-4.1-2.2c-0.5-0.7-0.9-1.5-1-2.3
			c-0.2-0.9-0.1-1.7,0.1-2.6c0.4-1.7,1.6-3.1,3.2-3.8c-0.4-1.5-0.3-3,0.3-4.4c0.8-1.9,2.7-3.2,4.8-3.1c1.3,0.1,2.6,0.5,3.7,1.3
			c0.6-0.8,1.4-1.6,2.3-2.1c1-0.6,2.2-1,3.4-1.1l0,0c1.3,0,2.5,0.4,3.6,1.2c0.4,0.3,0.8,0.7,1.1,1.1c0.4-0.7,1-1.3,1.6-1.8
			c0.8-0.6,1.7-1,2.7-1.2c1-0.1,2.1,0,3,0.5c1.5,0.7,2.6,2,3.2,3.6c2.1-0.2,4.2,0.9,5.3,2.7c0.5,0.9,0.8,1.9,0.8,2.9
			c0,1-0.3,2-0.7,3c-1.1,2-3.1,3.4-5.3,3.6c0.1,1.6-0.4,3.1-1.5,4.3c-0.8,0.8-1.7,1.4-2.8,1.6c-2.4,0.6-4.9,0.2-6.9-1.3
			c-0.1,0.2-0.2,0.5-0.3,0.7c-0.4,0.9-1.1,1.6-2,2c-0.9,0.4-1.9,0.5-2.8,0.2c-0.9-0.2-1.7-0.5-2.4-1
			C370,306.7,370,306.9,370.1,307.1z M358,322.7l-0.5,5.3h13.6l-0.5-5.3H358z M407,343.3l2.6,0.3l7.8-4.8l-6.5-4L407,343.3z
			 M412.9,344l16,1.9l-9.5-5.8L412.9,344z M411.4,330.2l6.8-7.8l-6.6-7.6h-0.2V330.2z M419.4,321l5.5-6.2h-11L419.4,321z
			 M427.2,314.8l-6.7,7.6l6.8,7.8v-15.4H427.2z M419.4,323.7l-7.4,8.5h14.8L419.4,323.7z M411.4,311.5l6.4-5.3l-6.4-5.3V311.5z
			 M419.4,304.9l5.3-4.4h-10.5C414.1,300.5,419.4,304.9,419.4,304.9z M427.3,300.9l-6.4,5.3l6.4,5.3V300.9z M419.4,307.5l-6.2,5.2
			h12.3L419.4,307.5z M419.4,337.5l5.3-3.2h-10.6L419.4,337.5L419.4,337.5z M427.9,334.7l-6.5,4l11.6,7.1L427.9,334.7z M429.4,306.2
			h11.5l-11.5-5.2V306.2z M409.4,300.8l-11.5,5.3h11.5L409.4,300.8L409.4,300.8z M429.4,315.3v5.1h11.4L429.4,315.3z M409.3,320.5
			v-5.2l-11.4,5.2H409.3z M426.4,298.2l-6.9-9l-7.1,9L426.4,298.2z M348.7,372.3h109.3v3.7H348.7V372.3z M363.6,352.8h8
			c0.6,0,1.1,0.5,1.1,1.1v10.1c0,0.6-0.5,1.1-1.1,1.1l0,0h-8c-0.6,0-1.1-0.5-1.1-1.1l0,0v-10.1C362.5,353.3,363,352.8,363.6,352.8
			L363.6,352.8z M386.9,352.8h8c0.6,0,1.1,0.5,1.1,1.1v10.1c0,0.6-0.5,1.1-1.1,1.1l0,0h-8c-0.6,0-1.1-0.5-1.1-1.1c0,0,0,0,0,0v-10.1
			C385.8,353.3,386.3,352.8,386.9,352.8L386.9,352.8z"
          />
        </g>
        <g id="central-unit-node">
          <path
            d="M969.5,147.2h-19.1c-2.6,0-4.7,2.1-4.7,4.7V171c0,2.6,2.1,4.7,4.7,4.7l19.1,0c2.6,0,4.7-2.1,4.7-4.7l0-19.1
			C974.2,149.3,972.1,147.2,969.5,147.2L969.5,147.2z M970,171c0,0.2-0.2,0.4-0.4,0.4h-19.1c-0.2,0-0.4-0.2-0.4-0.4v-19.1
			c0-0.2,0.2-0.4,0.4-0.4h19.1c0.2,0,0.4,0.2,0.4,0.4L970,171z"
          />
          <path
            d="M978.7,151.6c-0.4,0.4-0.6,1-0.6,1.5c0,0.6,0.2,1.1,0.6,1.5c0.4,0.4,1,0.6,1.5,0.6c0.6,0,1.1-0.2,1.5-0.6
			c0.4-0.4,0.6-1,0.6-1.5c0-0.6-0.2-1.1-0.6-1.5C980.9,150.8,979.4,150.8,978.7,151.6L978.7,151.6z"
          />
          <path
            d="M978.7,159.9c-0.4,0.4-0.6,1-0.6,1.5c0,0.6,0.2,1.1,0.6,1.5c0.4,0.4,1,0.6,1.5,0.6c0.6,0,1.1-0.2,1.5-0.6
			c0.4-0.4,0.6-1,0.6-1.5s-0.2-1.1-0.6-1.5C980.9,159.1,979.5,159.1,978.7,159.9L978.7,159.9z"
          />
          <path
            d="M978.7,168.3c-0.4,0.4-0.6,1-0.6,1.5c0,0.6,0.2,1.1,0.6,1.5c0.4,0.4,1,0.6,1.5,0.6c0.6,0,1.1-0.2,1.5-0.6
			c0.4-0.4,0.6-1,0.6-1.5c0-0.6-0.2-1.1-0.6-1.5C980.9,167.5,979.5,167.5,978.7,168.3L978.7,168.3z"
          />
          <path
            d="M938.3,151.6c-0.4,0.4-0.6,1-0.6,1.5c0,0.6,0.2,1.1,0.6,1.5c0.4,0.4,1,0.6,1.5,0.6c0.6,0,1.1-0.2,1.5-0.6
			c0.4-0.4,0.6-1,0.6-1.5c0-0.6-0.2-1.1-0.6-1.5C940.6,150.8,939.1,150.8,938.3,151.6L938.3,151.6z"
          />
          <path
            d="M938.3,159.9c-0.4,0.4-0.6,1-0.6,1.5c0,0.6,0.2,1.1,0.6,1.5c0.4,0.4,1,0.6,1.5,0.6c0.6,0,1.1-0.2,1.5-0.6
			c0.4-0.4,0.6-1,0.6-1.5s-0.2-1.1-0.6-1.5C940.6,159.1,939.1,159.1,938.3,159.9L938.3,159.9z"
          />
          <path
            d="M938.3,168.3c-0.4,0.4-0.6,1-0.6,1.5c0,0.6,0.2,1.1,0.6,1.5c0.4,0.4,1,0.6,1.5,0.6c0.6,0,1.1-0.2,1.5-0.6
			c0.4-0.4,0.6-1,0.6-1.5c0-0.6-0.2-1.1-0.6-1.5C940.6,167.5,939.1,167.5,938.3,168.3L938.3,168.3z"
          />
          <path
            d="M998.5,152.1c1.2,0,2.1-1,2.1-2.1s-1-2.1-2.1-2.1h-5.7v-7.2l5.7,0c1.2,0,2.1-1,2.1-2.1c0-1.2-1-2.1-2.1-2.1h-5.7v-2.9
			c0-2.6-2.1-4.7-4.7-4.7h-2.9h-4.3h-7.2h-4.3h-7.2h-4.3h-7.2h-4.3h-7.2h-4.3h-2.9c-2.6,0-4.7,2.1-4.7,4.7v2.9h-5.7
			c-1.2,0-2.1,1-2.1,2.1s1,2.1,2.1,2.1h5.7v7.2l-5.7,0c-1.2,0-2.1,1-2.1,2.1s1,2.1,2.1,2.1h5.7v7.2h-5.7c-1.2,0-2.1,1-2.1,2.1
			c0,1.2,1,2.1,2.1,2.1h5.7v7.2l-5.7,0c-1.2,0-2.1,1-2.1,2.1s1,2.1,2.1,2.1h5.7v7.2l-5.7,0c-1.2,0-2.1,1-2.1,2.1s1,2.1,2.1,2.1h5.7
			v2.9c0,2.6,2.1,4.7,4.7,4.7h2.9v5.7c0,1.2,1,2.1,2.1,2.1c1.2,0,2.1-1,2.1-2.1v-5.7h7.2l0,5.7c0,1.2,1,2.1,2.1,2.1s2.1-1,2.1-2.1
			v-5.7h7.2v5.7c0,1.2,1,2.1,2.1,2.1s2.1-1,2.1-2.1v-5.7h7.2l0,5.7c0,1.2,1,2.1,2.1,2.1s2.1-1,2.1-2.1v-5.7h7.2v5.7
			c0,1.2,1,2.1,2.1,2.1s2.1-1,2.1-2.1v-5.7h2.9c2.6,0,4.7-2.1,4.7-4.7v-2.9l5.7,0c1.2,0,2.1-1,2.1-2.1c0-1.2-1-2.1-2.1-2.1h-5.7
			v-7.2l5.7,0c1.2,0,2.1-1,2.1-2.1s-1-2.1-2.1-2.1h-5.7l0-7.2h5.7c1.2,0,2.1-1,2.1-2.1s-1-2.1-2.1-2.1h-5.7v-7.2L998.5,152.1z
			 M988.5,189.5c0,0.2-0.2,0.4-0.4,0.4h-56.2c-0.2,0-0.4-0.2-0.4-0.4v-56.2c0-0.2,0.2-0.4,0.4-0.4h56.2c0.2,0,0.4,0.2,0.4,0.4V189.5
			z"
          />
        </g>
      </g>
      <g id="dynamic-inputs">
        <path
          id="dynamic-val-battery"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="none"
          d="M928.7,371h64.2c2.8,0,5,5.5,5,12.3v11.5
		c0,6.8-2.3,12.3-5,12.3h-64.2c-2.8,0-5-5.5-5-12.3v-11.5C923.6,376.5,925.9,371,928.7,371z"
        />

        <rect
          id="dynamic-val-panel"
          x="493.5"
          rx="15"
          y="143"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          stroke="#000000"
          stroke-width="0.72"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-miterlimit="2.9998"
          width="100.4"
          height="41.1"
        />

        <rect
          id="dynamic-val-plant"
          x="493.5"
          y="267.8"
          rx="15"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          stroke="#000000"
          stroke-width="0.72"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-miterlimit="2.9998"
          width="100.4"
          height="41.1"
        />

        <rect
          id="dynamic-val-house"
          x="1313.3"
          y="172.7"
          rx="15"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          stroke="#000000"
          stroke-width="0.72"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-miterlimit="2.9998"
          width="100.4"
          height="41.1"
        />
      </g>
    </svg>
    <!-- eslint-enable -->
  </div>
</template>

<style>
#solar-scheme-container {
  #lines {
    path {
      stroke-width: 2px;
      stroke: rgba(0, 0, 0, 0.5);
    }
  }
  #nodes {
    * {
      fill: rgb(var(--prim-col-foreground-contrast));
      fill: #000000;
    }
    .node-background {
      fill: rgb(var(--prim-col-2));
    }
  }
  #dynamic-inputs {
    path {
      fill: none;
      stroke: none;
    }
  }
}

.dark {
  #solar-scheme-01 {
    > text {
      fill: rgb(var(--prim-col-foreground-contrast));
    }
    #lines {
      path {
        stroke-width: 2px;
        stroke: rgba(255, 255, 255, 0.65);
      }
    }
    #nodes {
      * {
        /*fill: rgb(var(--prim-col-foreground-contrast));*/
        fill: #ffffff;
      }
      .node-background {
        fill: rgb(var(--prim-col-2));
      }
    }
    #dynamic-inputs {
      rect {
        fill: rgb(var(--prim-col-foreground-2));
        stroke: none;
      }
    }
  }
}
</style>
