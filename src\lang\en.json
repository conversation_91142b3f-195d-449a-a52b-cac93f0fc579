{"alerts": {"title": "<PERSON><PERSON><PERSON>"}, "calendar": {"pick-a-day": "Pick a day", "pick-a-range": "Pick a range", "pick-date": "Pick a date", "pick-interval": "Pick an interval", "x-days": "{n} days | {n} day | {n} days", "x-months": "{n} months | {n} month | {n} months"}, "charts": {"ac-output": "AC Output", "battery-discharge": "Battery(W)(Discharge)", "contribution-ratio": "Contribution Ratio", "energy-metrics": "Energy Metrics", "feed-in": "Feed-in", "generation-income": "Generation & Income", "grid": "Grid", "in-house": "In-house", "in-house-kwh": "In-house(kWh)", "income-eur": "Income (EUR)", "load": "Load(W)", "load-consumption": "Load Consumption", "meter-buy": "Meter(W)(Buy)", "power": "Power", "pv": "PV(W)", "pv-bat": "PV + BAT", "pv-kwh": "PV(kWh)", "self-cons": "Self-Cons. Ratio", "self-cons-ratio": "Self-Cons. Ratio(%)", "sell-kwh": "Sell(kWh)", "soc": "SOC(%)", "solar-production": "Solar production"}, "installation": {"add-device": "Add device", "add-device-desc": "Enter your device pairing code. You can also assign a name to the device.", "add-device-tooltip": "To add a device, it is necessary to have an own installation created", "battery": "Battery", "battery-capacity": "Battery capacity", "classification": "Classification", "classification-values": {"on_grid": "On-grid", "hybrid": "Hybrid"}, "create-installation": "Create installation", "create-installation-desc": "Please fill in the name or description of the installation. You will be able to add your devices under the installation.", "device-counted": "devices | device | devices", "device-detail": "Device detail", "devices": {"inverter": "Inverter", "pairing": "Pairing"}, "edit-installation": "Edit installation", "energy-from-network": "Energy received from the network", "energy-to-network": "Energy sold to network", "form": {"device-add-already-created": "This device has already been registered.", "device-add-fail": "Failed to add device", "device-add-success": "The device has been successfully added. If it is successfully paired, it will appear under the installation.", "installation-fail-create": "Failed to create the installation", "installation-fail-edit": "Failed to update installation", "installation-share-fail": "Failed to share installation", "installation-share-success": "Activation email has been sent to {user}", "installation-share-user-exists": "The user is already assigned to the installation.", "installation-success-create": "Installation created successfully", "installation-success-edit": "Installation updated", "pv-capacity": "PV capacity", "panels-count": "Panels count", "battery-capacity": "Battery capacity"}, "house-consumption": "House consumption", "installations-shared-with-me": "Shared installations", "is-not-shared": "This installation is not shared to any user.", "last-update": "Last updated", "my-installations": "My installations", "new-device-added": "New device found", "no-installations": "No installations", "no-installations-desc": "Create an installation and add your first device to it", "paired-devices-counted": "paired devices | paired device | paired devices", "pairing-code": "Pairing code", "power": "Power", "produced-energy": "Produced energy", "pv-capacity": "PV capacity", "real-time-energy-flow": "Energy flow in real time", "share-installation": "Share installation", "share-installation-accepted": "The shared installation has been added to your dashboard.", "share-installation-desc": "After successfully submitting the form, an email will be sent to the user to activate the display of your installation in their dashboard.", "shared-installation": "Shared installation", "sharing": "Sharing", "title-base": "Installation", "title-counted": "installations | installation | installations", "title-plural": "Installations", "todays-summary": "Today's summary", "user-email": "User email", "remove-shared-user": "Remove viewing privileges", "add-device-tooltip-2": "Use the app for initial pairing. The devices will then be paired automatically.", "today-summary": "Today's summary", "total-summary": "Overall summary", "pick-a-day": "Select day", "plant-usage-chart": "Power plant utilization chart", "not-exists-or-forbidden": "The installation does not exist or you do not own it.", "energetic-blocks": "Energetic blocks", "my-plant": "My power plant", "photovoltaics": "Photovoltaics", "household": "Household", "my-electroinstallation": "My electroinstallation", "no-supported-devices": "No supported devices!", "device-online": "Device online", "device-offline": "Device is offline", "last-activity": "Last activity", "back-to-installation": "Back to installation"}, "login": {"current-password": "Current password", "errorLoggingIn": "<PERSON><PERSON> was not successful", "failed-to-reset-pw": "Error when requesting password change. Please try again later.", "forgot-password": "Forgot password", "forgot-password-desc": "After sending the form with an email, you will receive an email with a link to reset your password", "forgotten-password": "Forgotten password", "log-out": "Log out", "login": "<PERSON><PERSON>", "loginDesc": "To enter, it is necessary to authenticate using an e-mail and a password.", "min-pw-length": "Password must contain at least 8 characters", "needAccount": "Don't have an account?", "new-password": "New password", "pw-do-not-match": "Passwords do not match", "pw-helper": {"length": "Password must be at least 10 characters long.", "lowercase": "Password must contain at least one lowercase letter.", "number": "Password must contain at least one number.", "symbol": "Password must contain at least one symbol.", "uppercase": "Password must contain at least one uppercase letter."}, "pw-successfully-set": "The password has been successfully set", "reask-new-password": "New request for password change", "repeat-new-password": "Repeat new password", "reset-needed": "Reset needed", "reset-password": "Reset password", "reset-password-desc": "To set a new password, fill out the following form", "reset-pw-succ": "Your password was changed successfully", "signIn": "Sign in", "signUp": "Sign up", "token-revoked": "Your login has expired. Please refresh the application."}, "metrics": {"atk_battery_charge": "Battery percentage", "atk_battery_power": "Battery power", "atk_battery_power+": "Battery draining", "atk_battery_power-": "Battery charging", "atk_grid_power": "Grid power", "atk_grid_power+": "Drawn power", "atk_grid_power-": "Excess power", "atk_solar_power": "Solar production", "total_load_power": "Consumption", "atk_grid_energy_buy": "Bought energy", "atk_grid_energy_sell": "Sold energy", "atk_solar_energy": "Produced energy", "atk_home_power": "Household load"}, "misc": {"422-try-again-later": "Too many requests. Please try again later.", "back-to-home": "Back to homepage", "cancel": "Cancel", "choose-from-menu": "Pick an item from menu", "code": "Code", "contacts": "Contacts", "continue": "Continue", "continue-prompt": "Do you want to continue?", "copy-to-clipboard": "Copy to clipboard", "create": "Create", "created": "Date created", "dashboard": "Dashboard", "date-created": "Date created", "description": "Description", "edit": "Edit", "failed-to-get-data": "Failed to get data from the server", "form-sent-successfully": "Formular was sent successfully", "getting-ready": "Getting ready", "hide": "<PERSON>de", "household": "Household", "loading": "Loading", "locality": "Locality", "mail": "Mail", "mode": "Mode", "name": "Name", "name-surname": "Name and surname", "own-title": "Custom title", "password": "Password", "phone": "Phone number", "photo": "Photo", "remove": "Remove", "reset": "Reset", "save": "Save", "search": "Search", "send": "Send", "settings": "Settings", "show": "Show", "show-more": "Show more", "start": "Start", "surname": "Surname", "theme": "Theme", "title": "Title", "today": "Today", "token-not-valid": "This password request has already expired", "type": "Type", "unassigned": "Not assigned", "undefined": "Undefined", "upload-photo": "Upload photo", "select-option": "Select an option", "24h": "24h", "image": "Image", "unknown-state": "Unknown state", "last-24h": "Last 24 hours", "no-data": "No data", "apps": "Apps", "app-version": "App version", "api-version": "API Version"}, "monitoring": {"title": "Monitoring"}, "register": {"account-create-desc": "To create a new account, please fill out the form below", "account-create-title": "Create an account", "confirm-password": "Repeat password", "create-account": "Create account", "email-verified": "Email address verified successfully", "failed-to-register": "An error occurred during registration process. Please try again later.", "failed-verify": "Failed to verify email address", "success-register": "Registration successfull", "success-register-desc": "Please confirm your email address to complete the registration", "verifying-email": "Verifying email address", "mail-already-used": "This e-mail is already being used for other account."}, "reports": {"title": "Reports"}, "role-management": {"delete-role-action": "Are you sure you want to delete role {name}?", "edit-roles-failed": "Changes were not saved. Server side error.", "failed-create-role": "Failed to create role. Server side error.", "failed-delete-role": "Failed to delete role.", "role": "Role", "role-name": "Role name", "title": "Role management"}, "settings": {"change-password": "Change password", "pw-change": "Password change", "choose-platform": "Choose platform"}, "user": {"delete-user": {"modal-desc": "This action cannot be undone. This will permanently delete the account and remove data from our servers.", "title": "Are you sure you want to delete user {name}?"}, "my-account": "My account"}, "user-management": {"actions": "Actions", "create-user": "Create user", "create-user-desc": "Provide user details. When you click save, an email with activation link will be sent to user which then can choose his password.", "delete-user-action": "Are you sure you want to delete user {name}?", "edit-failed": "Changes were not saved. Server side error.", "employee-id": "Employee ID", "permissions": "Permissions", "roles": "Roles", "success-delete-user": "Successfully removed user", "title": "User management", "user": "User", "user-create-fail": "An error ocurred when creating new user", "user-create-success": "New user has been created", "user-delete-fail": "Failed to delete user"}, "tuya": {"scan-qr-instruction": "Scan the QR code using the Tuya Smart app", "waiting-for-auth": "Waiting for confirmation...", "auth-success": "Authentication successful!", "auth-failed": "Authentication failed or timed out."}, "device-detail": {"basic-information": "Basic Information", "identifier": "Identifier", "device-type": "Device type", "vendor": "<PERSON><PERSON><PERSON>", "model": "Model", "serial-number": "Serial number", "all-metrics": "All Metrics", "last-update": "Last update:", "history-charts": "History charts", "start": "Start:", "end": "End:", "aggregation": "Aggregation:", "no-aggregation": "No aggregation", "select-metric": "Select a metric", "average": "Average", "remove": "Remove", "add-metric": "Add Metric", "select-metric-message": "Please, select a metric to display the chart", "add-chart": "Add Chart", "aggregations": {"5_minutes": "5 minutes", "10_minutes": "10 minutes", "30_minutes": "30 minutes", "1_hour": "1 hour", "2_hours": "2 hours", "3_hours": "3 hours", "6_hours": "6 hours", "12_hours": "12 hours", "1_day": "1 day", "1_week": "1 week", "1_month": "1 month"}}}