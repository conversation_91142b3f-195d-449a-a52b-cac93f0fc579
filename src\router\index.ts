import { createRouter, createWebHistory, type RouteRecordNameGeneric } from 'vue-router';
import { type CustomRouteMeta, routeMap, routes } from '@/router/routes';
import { useAuthStore } from '@/stores/auth-store';
import { useInstallationStore } from '@/stores/installation-store.ts';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else if (to.name === from.name) {
      return;
    } else {
      return { top: 0 };
    }
  },
  routes,
});

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const installationStore = useInstallationStore();
  const toRouteMeta = to.meta as CustomRouteMeta;
  if (toRouteMeta.disabledForVerified && authStore.user && !authStore.isUnverified) {
    return next({
      name: routeMap.home.children.dashboardInstallations.name,
    });
  }
  if (!(([routeMap.verifyMail.name, routeMap.verify.name] as RouteRecordNameGeneric[]).includes(to.name)) && authStore.user && authStore.isUnverified) {
    return next({
      name: routeMap.verifyMail.name,
      query: { ...to.query, redirect: to.fullPath },
    });
  }
  if (to.name === routeMap.home.children.dashboardInstallations.name && installationStore.installations?.length && installationStore.ownedInstallations?.length === 1) {
    return next({ name: routeMap.home.children.installation.name, params: { installationId: installationStore.ownedInstallations[0].id } });
  }
  if (to.name === routeMap.home.children.dashboardInstallations.name || authStore.user || toRouteMeta?.authNotRequired) {
    return next();
  } else {
    return next({
      name: routeMap.home.children.dashboardInstallations.name,
      query: { ...to.query, redirect: to.fullPath },
    });
  }
});

export default router;
