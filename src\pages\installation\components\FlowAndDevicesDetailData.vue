<script lang="ts" setup>
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';
import SolarScheme2DNew from './SolarScheme2DNew.vue';
import type { InstallationDetailData } from '../types/installation-types';
import type { WaterHeaterCurrentDataTransformed } from '@/util/types/api-responses-heater';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
  waterHeaterCurrentData: WaterHeaterCurrentDataTransformed,
  installationDetail: InstallationDetailData,
}

defineProps<Props>();

</script>

<template>
  <div class="w-full h-full relative flex flex-col">
    <SolarScheme2DNew
      :inverter-current-data="inverterCurrentData"
      :installation-detail="installationDetail"
      :water-heater-current-data="waterHeaterCurrentData"
    />
  </div>
</template>
