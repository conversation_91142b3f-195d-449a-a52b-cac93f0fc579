<script setup lang="ts">
import { SVG, type Rect, type Circle, type Svg, type Path } from '@svgdotjs/svg.js';
import { computed, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { isLightModeEnabled } from '@/composables/theme';
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';

interface SchemeData {
  pathId: string;
  value: number;
  dots?: number;
  dynamicInputId?: string;
  dynamicInputMetric?: string;
  oppositeSvgPath?: boolean;
  meta?: Record<'stateOfCharge', number>,
}

interface Scheme {
  solarPanel: SchemeData;
  plant: SchemeData;
  battery: SchemeData;
  house: SchemeData;
}

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
}

const { inverterCurrentData } = defineProps<Props>();
const schemeContainer = ref<HTMLDivElement | null>(null);
const solarScheme = ref<SVGSVGElement | null>(null);
const svgJsEl = ref<Svg>();
const strokeCoefficient = 0.1; // 0.020

// const computedStrokeWidths = computed(() => {
//   const houseLoad = inverterCurrentData.metrics.backup_total_load_power.value > 10 ? inverterCurrentData.metrics.backup_total_load_power.value : inverterCurrentData.metrics.total_load_power.value;
//   return {
//     plant: `${Math.max(Math.abs(inverterCurrentData.metrics.atk_grid_power.value) / 200, 1) * strokeCoeficient}rem`,
//     house: `${Math.max(Math.abs(houseLoad) / 200, 1) * strokeCoeficient}rem`,
//     solar: `${Math.max(Math.abs(inverterCurrentData.metrics.atk_solar_power.value) / 200, 1) * strokeCoeficient}rem`,
//     battery: `${Math.max(Math.abs(inverterCurrentData.metrics.atk_battery_power.value) / 200, 1) * strokeCoeficient}rem`,
//   };
// });

const computedStrokeWidths = computed(() => {
  const houseLoad = inverterCurrentData.metrics.backup_total_load_power.value > 10 ? inverterCurrentData.metrics.backup_total_load_power.value : inverterCurrentData.metrics.total_load_power.value;

  const values = [
    Math.abs(inverterCurrentData.metrics.atk_grid_power.value),
    Math.abs(houseLoad),
    Math.abs(inverterCurrentData.metrics.atk_solar_power.value),
    Math.abs(inverterCurrentData.metrics.atk_battery_power.value),
  ];
  const averageValue = values.reduce((sum, value) => sum + value, 0) / values.length;
  const adjustedStrokeCoefficient = strokeCoefficient * (200 / averageValue);

  return {
    plant: `${Math.max(Math.abs(inverterCurrentData.metrics.atk_grid_power.value) / 200, 1) * adjustedStrokeCoefficient}rem`,
    house: `${Math.max(Math.abs(houseLoad) / 200, 1) * adjustedStrokeCoefficient}rem`,
    solar: `${Math.max(Math.abs(inverterCurrentData.metrics.atk_solar_power.value) / 200, 1) * adjustedStrokeCoefficient}rem`,
    battery: `${Math.max(Math.abs(inverterCurrentData.metrics.atk_battery_power.value) / 200, 1) * adjustedStrokeCoefficient}rem`,
  };
});

const computedOpacity = computed(() => {
  const houseLoad = inverterCurrentData.metrics.backup_total_load_power.value > 10 ? inverterCurrentData.metrics.backup_total_load_power.value : inverterCurrentData.metrics.total_load_power.value;
  return {
    plant: inverterCurrentData.metrics.atk_grid_power.value !== 0 ? 1 : 0.4,
    house: houseLoad !== 0 ? 1 : 0.4,
    solar: inverterCurrentData.metrics.atk_solar_power.value !== 0 ? 1 : 0.4,
    battery: inverterCurrentData.metrics.atk_battery_power.value !== 0 ? 1 : 0.4,
  };
});

// Define reactive schemeData structure
const schemeData: Scheme = reactive<Scheme>({
  solarPanel: {
    pathId: 'solar-central',
    value: inverterCurrentData.metrics.atk_solar_power.value,
    dots: 10,
    dynamicInputId: 'dynamic-val-panel',
    dynamicInputMetric: 'W',
    oppositeSvgPath: true,
  },
  plant: {
    pathId: 'plant-central',
    value: inverterCurrentData.metrics.atk_grid_power.value,
    dots: 18,
    dynamicInputId: 'dynamic-val-plant',
    dynamicInputMetric: 'W',
    oppositeSvgPath: false,
  },
  battery: {
    pathId: 'battery-central',
    value: inverterCurrentData.metrics.atk_battery_power.value,
    dots: 8,
    dynamicInputId: 'dynamic-val-battery',
    dynamicInputMetric: 'W',
    oppositeSvgPath: true, // This path starts from the center, so dots move in reverse
    meta: {
      stateOfCharge: inverterCurrentData.metrics.atk_battery_charge.value,
    },
  },
  house: {
    pathId: 'house-central',
    value: inverterCurrentData.metrics.backup_total_load_power.value > 10 ? inverterCurrentData.metrics.backup_total_load_power.value : inverterCurrentData.metrics.total_load_power.value,
    dots: 7,
    dynamicInputId: 'dynamic-val-house',
    dynamicInputMetric: 'W',
    oppositeSvgPath: false,
  },
});

const getSvgPaths = () => ({
  solarPanel: svgJsEl.value!.findOne(`#${schemeData.solarPanel.pathId}`) as Path,
  plant: svgJsEl.value!.findOne(`#${schemeData.plant.pathId}`) as Path,
  battery: svgJsEl.value!.findOne(`#${schemeData.battery.pathId}`) as Path,
  house: svgJsEl.value!.findOne(`#${schemeData.house.pathId}`) as Path,
});

// Function to insert the dynamic value and metric inside the corresponding rectangles
const updateDynamicInputs = (svg: Svg): void => {
  // Clear existing text from dynamic inputs
  svg.find('text').forEach(text => text.remove()); // Remove all text elements

  (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
    const data = schemeData[key];
    const valueText = `${Math.abs(data.value)} ${data.dynamicInputMetric}`; // Combine value and metric

    // Find the rectangle by dynamicInputId
    const dynamicInput = svg.findOne(`#${data.dynamicInputId}`) as unknown as Rect;

    if (dynamicInput) {
      // Add text inside the rectangle for value + metric
      const bbox = dynamicInput.bbox();
      svg
        .text(valueText)
        .font({ size: 20, family: 'Roboto', anchor: 'middle' })
        .center(bbox.cx, bbox.cy); // Center the text inside the rectangle
    }
  });

  const batteryPercentageInput = svg.findOne('#dynamic-val-battery-percentage') as unknown as Rect;
  if (batteryPercentageInput) {
    // Add text inside the rectangle for value + metric
    const bbox = batteryPercentageInput.bbox();
    svg
      .text(`${inverterCurrentData.metrics.atk_battery_charge.value}%`)
      .font({ size: 20, family: 'Roboto', anchor: 'middle' })
      .center(bbox.cx, bbox.cy); // Center the text inside the rectangle
  }
};

const animationFrameIds: Record<string, number | null> = {
  solarPanel: null,
  plant: null,
  battery: null,
  house: null,
};

const animateDotsOnPath = (
  path: Path,
  color: string,
  value: number,
  dotCount: number = 5,
  oppositeSvgPath: boolean = false,
): void => {
  if (!path || value === 0) {
    return;
  }

  const length = path.length();
  const spacing = length / dotCount;

  // Select or create dots
  const dots = svgJsEl.value!.find(`circle.dot-${path.id()}`) as unknown as Circle[];

  if (dots.length < dotCount) {
    for (let i = dots.length; i < dotCount; i++) {
      const dot = (path.parent()! as Svg)
        .circle(15)
        .fill(color)
        .addClass(`dot-${path.id()}`);
      dots.push(dot);
    }
  }

  // Stop any existing animation for this path
  if (animationFrameIds[path.id()]) {
    cancelAnimationFrame(animationFrameIds[path.id()]!);
  }

  // Track the start time
  const start = performance.now();
  const baseSpeed = 7000 - Math.abs(value);
  const speed = baseSpeed * (length / 500);

  const animateAllDots = (time: number) => {
    const elapsed = (time - start) % speed;

    dots.forEach((dot, index) => {
      const pos = (elapsed / speed + index * (spacing / length)) % 1;
      let point;

      // Re-evaluate direction during each frame
      const isReverseDirection =
        (value < 0 && !oppositeSvgPath) || (value > 0 && oppositeSvgPath);

      if (isReverseDirection) {
        point = path.pointAt(length - pos * length); // Reverse direction
      } else {
        point = path.pointAt(pos * length); // Forward direction
      }

      if (point) {
        dot.center(point.x, point.y);
      }
    });

    // Store the current animation frame ID
    animationFrameIds[path.id()] = requestAnimationFrame(animateAllDots);
  };

  // Start the new animation
  animationFrameIds[path.id()] = requestAnimationFrame(animateAllDots);
};

const initializeAndAnimatePaths = (): void => {
  const dotColor: string = `rgb(${getComputedStyle(document.body)
    .getPropertyValue('--prim-col-foreground-1')
    .replaceAll(' ', ',')})`;

  if (!solarScheme.value) {
    return;
  }

  svgJsEl.value = SVG(solarScheme.value); // Select the SVG element

  // Update text inputs for dynamic values
  updateDynamicInputs(svgJsEl.value);

  // Paths to animate
  const paths: Record<string, Path | null> = getSvgPaths();

  // Animate the dots for each path
  (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
    const path = paths[key];
    const value = schemeData[key].value;
    const oppositeSvgPath = schemeData[key].oppositeSvgPath;

    if (path) {
      // You can adjust the number of dots here for different paths
      animateDotsOnPath(path, dotColor, value, schemeData[key].dots, oppositeSvgPath);
    }
  });
};

const clearAnimations = () => {
  const paths: Record<string, Path | null> = getSvgPaths();
  (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
    const path = paths[key]!;

    svgJsEl.value!.find(`circle.dot-${path.id()}`).forEach(dot => dot.remove());
    if (animationFrameIds[path.id()]) {
      cancelAnimationFrame(animationFrameIds[path.id()]!); // Stop any ongoing animation
      animationFrameIds[path.id()] = null;
    }
  });
};

watch(isLightModeEnabled, () => {
  clearAnimations();
  initializeAndAnimatePaths(); // Reinitialize the animation on data change
});

watch(() => inverterCurrentData.metrics, () => {
  schemeData.battery.value = inverterCurrentData.metrics.atk_battery_power.value;
  schemeData.battery.meta!.stateOfCharge = inverterCurrentData.metrics.atk_battery_charge.value;
  schemeData.solarPanel.value = inverterCurrentData.metrics.atk_solar_power.value;
  schemeData.plant.value = inverterCurrentData.metrics.atk_grid_power.value;
  schemeData.house.value = inverterCurrentData.metrics.backup_total_load_power.value > 10 ? inverterCurrentData.metrics.backup_total_load_power.value : inverterCurrentData.metrics.total_load_power.value;

  // Define the dot color
  const dotColor = `rgb(${getComputedStyle(document.body)
    .getPropertyValue('--prim-col-foreground-1')
    .replaceAll(' ', ',')})`;

  // Check if the SVG element is initialized
  if (solarScheme.value) {
    // Get all the paths
    const paths: Record<string, Path | null> = getSvgPaths();

    // Iterate through each path and update the dots accordingly
    (Object.keys(schemeData) as Array<keyof Scheme>).forEach(key => {
      const path = paths[key];
      const value = schemeData[key].value;
      const oppositeSvgPath = schemeData[key].oppositeSvgPath;

      if (path) {
        // If the value is 0, skip animating the dots
        if (value !== 0) {
          animateDotsOnPath(path, dotColor, value, schemeData[key].dots, oppositeSvgPath);
        } else {
          // If the value is 0, remove the dots to stop animation
          svgJsEl.value!.find(`circle.dot-${path.id()}`).forEach(dot => dot.remove());
          if (animationFrameIds[path.id()]) {
            cancelAnimationFrame(animationFrameIds[path.id()]!); // Stop any ongoing animation
            animationFrameIds[path.id()] = null;
          }
        }
      }
    });

    // Optionally, update the dynamic input values (if necessary)
    updateDynamicInputs(svgJsEl.value!);
  }
});

onMounted((): void => {
  initializeAndAnimatePaths();
});

onBeforeUnmount(() => {
  clearAnimations();
});
</script>

<template>
  <div
    id="solar-scheme-container"
    ref="schemeContainer"
    class="flex-1 relative"
  >
    <!-- eslint-disable no-mixed-spaces-and-tabs -->
    <svg
      id="solar-scheme-01"
      ref="solarScheme"
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      viewBox="0 0 1920 450.9005432"
      xml:space="preserve"
    >
      <g id="lines">
        <path
          id="plant-central"
          fill="none"
          stroke="#000000"
          stroke-miterlimit="10"
          d="M1127.373291,230.1113586L948.6087036,394
		L679.2608643,239.2268219l-98.3110352,63.1139679"
        />
        <path
          id="solar-central"
          fill="none"
          stroke="#000000"
          stroke-miterlimit="10"
          d="M1116.6025391,196.8708038
		l-99.5727539-56.9655457L900.2304688,244.0170135l0.8547974-106.7976685"
        />
        <path
          id="house-central"
          fill="none"
          stroke="#000000"
          stroke-miterlimit="10"
          d="M1186.6572266,169.3843689
		l41.1053467-36.8347626l141.8127441,81.1310883"
        />
        <path
          id="battery-central"
          fill="none"
          stroke="#000000"
          stroke-miterlimit="10"
          d="M1175.5651855,228.8695679
		l134.4782715,76.4347839l-33.9129639,35.6739197l55.8586426,33.8478088"
        />
      </g>
      <g id="Layer_7" />
      <g id="nodes">
        <g id="plant-node">
          <g>
            <g>
              <polygon
                fill="#1C1C1C"
                points="595.5994873,329.0259094 519.529541,286.8096619 416.8006287,345.2435303
					492.8684692,388.5134888 				"
              />
            </g>
            <g>
              <polygon
                fill="#F2D323"
                points="396.3095703,343.8250732 525.9124146,271.4572144 619.9215088,324.2092896
					490.3186951,397.8931885 				"
              />
              <polygon
                fill="#777F7F"
                points="401.4418945,343.8536377 525.9082031,274.3512268 614.793396,324.2130737
					490.3287659,394.9744263 				"
              />
              <polygon
                fill="#DBDBDB"
                points="490.3186951,403.0830078 619.9215088,329.394928 619.9215088,324.2092896
					490.3186951,397.8931885 				"
              />
              <polygon
                fill="#AAB7B7"
                points="490.3186951,403.0830078 396.3095703,349.0069275 396.3095703,343.8250732
					490.3186951,397.8931885 				"
              />
            </g>
            <polygon
              fill="#566472"
              points="484.1898193,298.4234619 526.4207764,274.8348083 568.2579956,298.497345
				526.0148315,322.512085 			"
            />
            <g>
              <g>
                <g>
                  <polygon
                    fill="#EFF3F3"
                    points="492.768158,295.0576172 526.7058105,276.1059265 560.3294678,295.1193237
							526.3796387,314.4152527 						"
                  />
                  <g>
                    <polygon
                      fill="#138391"
                      points="526.3796387,317.5309143 560.3294678,298.226593 560.3294678,295.1193237
								526.3796387,314.4152527 							"
                    />
                    <polygon
                      fill="#1F193B"
                      points="526.3796387,317.5309143 492.768158,298.1690674 492.768158,295.0576172
								526.3796387,314.4152527 							"
                    />
                    <g>
                      <g>
                        <polygon
                          fill="#AAB7B7"
                          points="526.5026245,313.8497925 526.5026245,243.6010132 493.8956909,224.9238586
										493.8956909,295.168457 									"
                        />
                        <polygon
                          fill="#DBDBDB"
                          points="526.5026245,313.8497925 526.5026245,243.6010132 559.1099854,224.9238586
										559.1099854,295.168457 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="501.9858093,233.438385 495.9904175,229.9743042 495.6541748,230.2694244
										501.6558838,242.8222961 501.9858093,242.5267792 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="501.6558838,233.7255249 495.6541748,230.2694244 495.6541748,239.3661957
										501.6558838,242.8222961 									"
                        />
                        <polygon
                          fill="#566472"
                          points="496.3652954,238.9602661 496.3652954,231.4951935 500.9506226,234.1394348
										500.9506226,241.6045074 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="495.9081421,233.4056396 495.9081421,234.1272583 501.3259277,237.2429199
											501.3259277,236.5213013 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="495.9081421,236.0171356 495.9081421,236.7345581 501.3259277,239.8539886
											501.3259277,239.1365814 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="498.9683838,232.6185455 498.2862549,232.2210083 498.2862549,240.3338013
										498.9683838,240.7191772 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="523.7374878,245.6424255 517.7362061,242.1947174 517.4058228,242.4818573
										523.4033203,255.034317 523.7374878,254.739212 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="523.4033203,245.9375458 517.4058228,242.4818573 517.4058228,251.5786285
										523.4033203,255.034317 									"
                        />
                        <polygon
                          fill="#566472"
                          points="518.111084,251.1685028 518.111084,243.7076263 522.7006226,246.3434753
										522.7006226,253.8085327 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="517.6602783,245.6180878 517.6602783,246.335495 523.0775757,249.4549255
											523.0775757,248.7375183 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="517.6602783,248.2291565 517.6602783,248.9465637 523.0775757,252.0664215
											523.0775757,251.3490143 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="520.7141724,244.8309784 520.0336914,244.4414215 520.0336914,252.542038
										520.7141724,252.9316101 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="531.045166,245.0643768 537.0426025,241.6087036 537.3789063,241.9038086
										531.3771973,254.4524994 531.045166,254.1573792 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="531.3771973,245.3599091 537.3789063,241.9038086 537.3789063,250.9963837
										531.3771973,254.4524994 									"
                        />
                        <polygon
                          fill="#566472"
                          points="536.6673584,250.5904541 536.6673584,243.1216125 532.0820313,245.7654266
										532.0820313,253.2309113 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="537.1245117,245.0358429 537.1245117,245.7574463 531.7071533,248.8689117
											531.7071533,248.1556854 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="537.1245117,247.6473236 537.1245117,248.3689423 531.7071533,251.4841766
											531.7071533,250.7667694 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="534.06427,244.2445374 534.7468262,243.8553925 534.7468262,251.9597931
										534.06427,252.3535614 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="549.2344971,234.7258759 555.2362061,231.2697754 555.5619507,231.5606842
										549.5644531,244.1135712 549.2344971,243.8184509 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="549.5644531,235.0209808 555.5619507,231.5606842 555.5619507,240.6574554
										549.5644531,244.1135712 									"
                        />
                        <polygon
                          fill="#566472"
                          points="554.8609009,240.247757 554.8609009,232.7826843 550.2713623,235.4306946
										550.2713623,242.8957672 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="555.3075562,234.6969147 555.3075562,235.4143219 549.9006958,238.5341797
											549.9006958,237.8167725 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="555.3075562,237.3084106 555.3075562,238.0258179 549.9006958,241.1452484
											549.9006958,240.424057 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="552.2578125,233.9098053 552.934082,233.5164642 552.934082,241.6170959
										552.2578125,242.0146332 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="540.4470825,239.8334198 546.4445801,236.3735352 546.7745361,236.6686554
										540.7728271,249.2173309 540.4470825,248.9264221 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="540.7728271,240.128952 546.7745361,236.6686554 546.7745361,245.7654266
										540.7728271,249.2173309 									"
                        />
                        <polygon
                          fill="#566472"
                          points="546.0697021,245.3557129 546.0697021,237.8906555 541.484375,240.5344696
										541.484375,247.9999542 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="546.5205688,239.8006744 546.5205688,240.5222931 541.1091309,243.6421509
											541.1091309,242.9247284 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="546.5205688,242.4163666 546.5205688,243.1337891 541.1091309,246.2532196
											541.1091309,245.5357971 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="543.4661865,239.0177765 544.1470947,238.6244354 544.1470947,246.7330322
										543.4661865,247.1183929 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="510.0062561,237.9683075 504.0050049,234.5084229 503.6746216,234.8035431
										509.6704407,247.3560028 510.0062561,247.0608826 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="509.6704407,238.2634125 503.6746216,234.8035431 503.6746216,243.9003143
										509.6704407,247.3560028 									"
                        />
                        <polygon
                          fill="#566472"
                          points="504.3798523,243.4943848 504.3798523,236.0293121 508.9693909,238.6693573
										508.9693909,246.1386108 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="503.9269104,237.9397583 503.9269104,238.6571808 509.3463745,241.7766113
											509.3463745,241.0592041 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="503.9269104,240.5508423 503.9269104,241.2724457 509.3463745,244.3881073
											509.3463745,243.6706848 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="506.9829712,237.1526642 506.3066711,236.7588959 506.3066711,244.8679199
										506.9829712,245.2532806 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="515.2699585,240.9773407 509.2745667,237.5216522 508.938324,237.8167725
										514.9362183,250.365036 515.2699585,250.0741119 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="514.9362183,241.2724457 508.938324,237.8167725 508.938324,246.9135437
										514.9362183,250.365036 									"
                        />
                        <polygon
                          fill="#566472"
                          points="509.6393738,246.503418 509.6393738,239.0425415 514.2351685,241.6783905
										514.2351685,249.1518402 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="509.1927185,240.9525757 509.1927185,241.6699829 514.605896,244.7898407
											514.605896,244.0724335 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="509.1927185,243.5640717 509.1927185,244.2814789 514.605896,247.4013367
											514.605896,246.679718 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="512.2429199,240.1616974 511.5724792,239.7679291 511.5724792,247.8769531
										512.2429199,248.266098 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="501.9858093,252.2343445 495.9904175,248.778656 495.6541748,249.073761
										501.6558838,261.6224365 501.9858093,261.3273315 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="501.6558838,252.5256653 495.6541748,249.073761 495.6541748,258.1663513
										501.6558838,261.6224365 									"
                        />
                        <polygon
                          fill="#566472"
                          points="496.3652954,257.7608337 496.3652954,250.2953491 500.9506226,252.935379
										500.9506226,260.4008789 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="495.9081421,252.2057953 495.9081421,252.9274139 501.3259277,256.0430603
											501.3259277,255.3256531 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="495.9081421,254.8172913 495.9081421,255.5346985 501.3259277,258.6583252
											501.3259277,257.9367371 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="498.9683838,251.4187012 498.2862549,251.0253601 498.2862549,259.1259766
										498.9683838,259.5193176 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="523.7374878,264.4467773 517.7362061,260.9910889 517.4058228,261.2861938
										523.4033203,273.8348694 523.7374878,273.5393677 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="523.4033203,264.7380981 517.4058228,261.2861938 517.4058228,270.3745728
										523.4033203,273.8348694 									"
                        />
                        <polygon
                          fill="#566472"
                          points="518.111084,269.9648743 518.111084,262.507782 522.7006226,265.1478271
										522.7006226,272.6128845 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="517.6602783,264.4182434 517.6602783,265.1356506 523.0775757,268.2554932
											523.0775757,267.5380859 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="517.6602783,267.0297241 517.6602783,267.7471313 523.0775757,270.8665771
											523.0775757,270.1491699 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="520.7141724,263.631134 520.0336914,263.2373657 520.0336914,271.3379822
										520.7141724,271.7359619 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="531.045166,263.8565674 537.0426025,260.408844 537.3789063,260.7001648
										531.3771973,273.2484436 531.045166,272.9533386 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="531.3771973,264.1600647 537.3789063,260.7001648 537.3789063,269.7927551
										531.3771973,273.2484436 									"
                        />
                        <polygon
                          fill="#566472"
                          points="536.6673584,269.3868103 536.6673584,261.9217529 532.0820313,264.5655823
										532.0820313,272.0310669 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="537.1245117,263.8317871 537.1245117,264.5534058 531.7071533,267.6732483
											531.7071533,266.9516296 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="537.1245117,266.4432678 537.1245117,267.1648865 531.7071533,270.2805481
											531.7071533,269.5673218 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="534.06427,263.0446777 534.7468262,262.6555481 534.7468262,270.7599487
										534.06427,271.1495056 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="549.2344971,253.5260162 555.2362061,250.0657196 555.5619507,250.3612518
										549.5644531,262.9137268 549.2344971,262.6228027 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="549.5644531,253.8211365 555.5619507,250.3612518 555.5619507,259.4580383
										549.5644531,262.9137268 									"
                        />
                        <polygon
                          fill="#566472"
                          points="554.8609009,259.0520935 554.8609009,251.5828247 550.2713623,254.227066
										550.2713623,261.6921387 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="555.3075562,253.4970551 555.3075562,254.2106934 549.9006958,257.3343201
											549.9006958,256.6169128 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="555.3075562,256.108551 555.3075562,256.8259583 549.9006958,259.9458313
											549.9006958,259.2283936 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="552.2578125,252.7061768 552.934082,252.3166199 552.934082,260.4172363
										552.2578125,260.8147888 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="540.4470825,258.6340027 546.4445801,255.1736908 546.7745361,255.469223
										540.7728271,268.0216675 540.4470825,267.7265625 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="540.7728271,258.9291077 546.7745361,255.469223 546.7745361,264.5655823
										540.7728271,268.0216675 									"
                        />
                        <polygon
                          fill="#566472"
                          points="546.0697021,264.1600647 546.0697021,256.6907959 541.484375,259.335022
										541.484375,266.8001099 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="546.5205688,258.5970459 546.5205688,259.3182373 541.1091309,262.4381104
											541.1091309,261.724884 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="546.5205688,261.2165222 546.5205688,261.9297485 541.1091309,265.053772
											541.1091309,264.3363647 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="543.4661865,257.8141479 544.1470947,257.4245911 544.1470947,265.5289917
										543.4661865,265.9143372 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="510.0062561,256.7684631 504.0050049,253.3085785 503.6746216,253.6078796
										509.6704407,266.1565552 510.0062561,265.8610229 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="509.6704407,257.0635681 503.6746216,253.6078796 503.6746216,262.70047
										509.6704407,266.1565552 									"
                        />
                        <polygon
                          fill="#566472"
                          points="504.3798523,262.2945251 504.3798523,254.8252563 508.9693909,257.4695129
										508.9693909,264.9345703 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="503.9269104,256.7398987 503.9269104,257.4615173 509.3463745,260.577179
											509.3463745,259.8597717 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="503.9269104,259.3514099 503.9269104,260.0726013 509.3463745,263.1882629
											509.3463745,262.4708252 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="506.9829712,255.9486084 506.3066711,255.5590515 506.3066711,263.6638794
										506.9829712,264.0492249 									"
                        />
                      </g>
                      <g>
                        <polygon
                          fill="#EFF3F3"
                          points="515.2699585,259.7816772 509.2745667,256.3218079 508.938324,256.6169128
										514.9362183,269.1693726 515.2699585,268.8742676 									"
                        />
                        <polygon
                          fill="#BDC3C7"
                          points="514.9362183,260.0726013 508.938324,256.6169128 508.938324,265.7136841
										514.9362183,269.1693726 									"
                        />
                        <polygon
                          fill="#566472"
                          points="509.6393738,265.2997742 509.6393738,257.8426819 514.2351685,260.4785461
										514.2351685,267.9436035 									"
                        />
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="509.1927185,259.7531433 509.1927185,260.4663696 514.605896,263.5899963
											514.605896,262.8725891 										"
                          />
                        </g>
                        <g>
                          <polygon
                            fill="#BDC3C7"
                            points="509.1927185,262.3642273 509.1927185,263.0816345 514.605896,266.2014771
											514.605896,265.4840698 										"
                          />
                        </g>
                        <polygon
                          fill="#BDC3C7"
                          points="512.2429199,258.9576416 511.5724792,258.5680847 511.5724792,266.6729126
										512.2429199,267.0666504 									"
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
            <polygon
              fill="#EDEDED"
              points="493.8956909,224.9238586 526.5026245,243.6010132 559.1099854,224.9238586
				526.5026245,206.2425079 			"
            />
            <polygon
              fill="#138391"
              points="498.7450562,224.9238586 526.5026245,240.8257904 554.2564087,224.9238586
				526.5026245,209.0219116 			"
            />
            <polygon
              fill="#138391"
              points="526.5026245,240.8257904 554.2564087,224.9238586 526.5026245,209.0219116 			"
            />
            <polygon
              fill="#566472"
              points="501.4589844,226.4774933 526.5026245,240.8257904 551.5466919,226.4774933
				526.5026245,212.1212006 			"
            />
            <polygon
              fill="#AAB7B7"
              points="503.1091614,227.42453 526.5026245,240.8257904 549.8901978,227.42453 526.5026245,214.0232544
							"
            />
            <g>
              <path
                fill="#1F193B"
                d="M530.1409302,125.1875763c0,2.9271698-18.7632141,2.9271698-18.7632141,0
					c0,0-3.6282349,93.3903656-3.6282349,96.723053c0,4.054306,5.8237,7.35466,13.0100403,7.35466
					s13.0095825-3.300354,13.0095825-7.35466C533.769104,218.5817261,530.1409302,125.1875763,530.1409302,125.1875763z"
              />
              <path
                fill="#B6D9D2"
                d="M511.3777161,125.1875763c0-2.9271774,4.1978455-5.3006516,9.3818054-5.3006516
					c5.1797485,0,9.3814087,2.3734741,9.3814087,5.3006516c0,2.9271698-4.2016602,5.3006439-9.3814087,5.3006439
					C515.5755615,130.4882202,511.3777161,128.1147461,511.3777161,125.1875763z"
              />
              <path
                fill="#566472"
                d="M514.4790649,125.1875763c0-1.795433,2.8100586-3.2428513,6.2804565-3.2428513
					c3.4720459,0,6.2821045,1.4474182,6.2821045,3.2428513c0,1.7954254-2.8100586,3.2466278-6.2821045,3.2466278
					C517.2891235,128.4342041,514.4790649,126.9830017,514.4790649,125.1875763z"
              />
              <path
                fill="#B6D9D2"
                d="M520.7595215,158.4342651c4.6239624,0,8.5985107-1.5166931,10.5211182-3.710083
					c-0.1229858-3.1358032-0.2354736-6.1490326-0.3463135-8.9653778c-1.6724243,2.2424927-5.5999756,3.8166962-10.1748047,3.8166962
					c-4.5794678,0-8.5065308-1.5742035-10.1752014-3.8166962c-0.1104126,2.8163452-0.2275391,5.8295746-0.3505249,8.9653778
					C512.1605835,156.917572,516.1351318,158.4342651,520.7595215,158.4342651z"
              />
              <path
                fill="#B6D9D2"
                d="M520.7595215,175.9473419c4.9295654,0,9.1744385-1.6153412,11.2263794-3.9602661
					c-0.1271973-3.3410797-0.2481079-6.5549774-0.3710938-9.5639954c-1.7895508,2.394043-5.9769287,4.0790558-10.8552856,4.0790558
					c-4.888855,0-9.0762024-1.6850128-10.8594666-4.0790558c-0.1171265,3.0090179-0.2401123,6.2229156-0.3710632,9.5639954
					C511.5804749,174.3320007,515.819458,175.9473419,520.7595215,175.9473419z"
              />
              <path
                fill="#B6D9D2"
                d="M520.7595215,224.419693c-6.62677,0-12.0852356-2.8041687-12.8970947-6.423996
					c-0.0718079,2.172821-0.1129456,3.5585327-0.1129456,3.9149323c0,4.054306,5.8237,7.35466,13.0100403,7.35466
					s13.0095825-3.300354,13.0095825-7.35466c0-0.3563995-0.0390015-1.7421112-0.1166992-3.9149323
					C532.8447876,221.6155243,527.3862915,224.419693,520.7595215,224.419693z"
              />
            </g>
            <g>
              <path
                fill="#138391"
                d="M545.8644409,156.6879425c0,2.0250549-12.9789429,2.0250549-12.9789429,0
					c0,0-2.5149536,64.6240692-2.5149536,66.9282837c0,2.812149,4.0299683,5.0873871,9.0006104,5.0873871
					c4.9744873,0,9.0044556-2.275238,9.0044556-5.0873871C548.3756104,221.3120117,545.8644409,156.6879425,545.8644409,156.6879425
					z"
              />
              <path
                fill="#EDEDED"
                d="M532.885498,156.6879425c0-2.0292358,2.9024048-3.6647339,6.4856567-3.6647339
					c3.5870972,0,6.4932861,1.635498,6.4932861,3.6647339c0,2.0250549-2.906189,3.6689453-6.4932861,3.6689453
					C535.7879028,160.3568878,532.885498,158.7129974,532.885498,156.6879425z"
              />
              <path
                fill="#566472"
                d="M535.0255737,156.6879425c0-1.2379456,1.9452515-2.2462769,4.3455811-2.2462769
					c2.4020386,0,4.3494263,1.0083313,4.3494263,2.2462769c0,1.2379608-1.9473877,2.2467041-4.3494263,2.2504883
					C536.9708252,158.9384308,535.0255737,157.9259033,535.0255737,156.6879425z"
              />
              <path
                fill="#B6D9D2"
                d="M539.3711548,179.6943512c3.2017212,0,5.9563599-1.0494537,7.2866821-2.5665741
					c-0.0860596-2.1723938-0.1679077-4.2549591-0.2438965-6.2023468c-1.1582031,1.5494385-3.8721313,2.6400452-7.0427856,2.6400452
					c-3.1668701,0-5.8807373-1.0906067-7.0389404-2.6400452c-0.0759888,1.9473877-0.1578369,4.029953-0.2396851,6.2023468
					C533.4249268,178.6448975,536.1694946,179.6943512,539.3711548,179.6943512z"
              />
              <path
                fill="#B6D9D2"
                d="M539.3711548,191.8123474c3.4145508,0,6.3518066-1.1233521,7.7744751-2.7382813
					c-0.090271-2.3163757-0.1784058-4.5383148-0.2581787-6.6166687c-1.2358398,1.6560516-4.1323242,2.8163452-7.5162964,2.8163452
					c-3.3759155,0-6.2766113-1.1602936-7.5124512-2.8163452c-0.0797729,2.0783539-0.1679077,4.300293-0.2539673,6.6166687
					C533.0269775,190.6889954,535.9562378,191.8123474,539.3711548,191.8123474z"
              />
              <path
                fill="#566472"
                d="M539.3711548,227.6293793c-4.5853271,0-8.3608398-1.9431915-8.9246216-4.451828
					c-0.0512085,1.5045013-0.0759888,2.4637146-0.0759888,2.7055206c0,2.8163452,4.0299683,5.0873871,9.0006104,5.0873871
					c4.9744873,0,9.0044556-2.2710419,9.0044556-5.0873871c0-0.241806-0.0306396-1.2010193-0.0802002-2.7055206
					C547.7358398,225.6861877,543.9544067,227.6293793,539.3711548,227.6293793z"
              />
              <path
                fill="#B6D9D2"
                d="M539.3711548,225.3541412c-4.5853271,0-8.3608398-1.9347992-8.9246216-4.4396667
					c-0.0512085,1.5003204-0.0759888,2.4595337-0.0759888,2.7017517c0,2.812149,4.0299683,5.0873871,9.0006104,5.0873871
					c4.9744873,0,9.0044556-2.275238,9.0044556-5.0873871c0-0.242218-0.0306396-1.2014313-0.0802002-2.7017517
					C547.7358398,223.419342,543.9544067,225.3541412,539.3711548,225.3541412z"
              />
            </g>
            <g>
              <g>
                <polygon
                  fill="#566472"
                  points="516.9877319,234.0861206 526.5026245,239.5345306 536.0153809,234.0861206
						536.0153809,231.9913788 516.9877319,231.9913788 					"
                />
                <polygon
                  fill="#C2CECE"
                  points="526.5026245,237.4393768 526.5026245,226.493866 516.9877319,221.0416718
						516.9877319,231.9913788 					"
                />
                <polygon
                  fill="#DBDBDB"
                  points="526.5026245,237.4393768 526.5026245,226.493866 536.0153809,221.0416718
						536.0153809,231.9913788 					"
                />
                <polygon
                  fill="#EDEDED"
                  points="516.9877319,221.0416718 526.5026245,226.493866 536.0153809,221.0416718
						526.5026245,215.5894775 					"
                />
                <polygon
                  fill="#B6D9D2"
                  points="520.1608887,221.0416718 526.5026245,224.6694641 532.8342896,221.0416718
						526.5026245,217.4134521 					"
                />
                <polygon
                  fill="#138391"
                  points="526.5026245,224.6694641 532.8342896,221.0416718 526.5026245,217.4134521 					"
                />
                <polygon
                  fill="#566472"
                  points="522.0751343,222.1196747 526.5026245,224.6530914 530.9242554,222.1196747
						526.5026245,219.582077 					"
                />
                <polygon
                  fill="#AAB7B7"
                  points="522.3664551,222.2880096 526.5026245,224.6530914 530.6350098,222.2880096
						526.5026245,219.9183197 					"
                />
                <polygon
                  fill="#F7F7F7"
                  points="520.1608887,221.0416718 526.5026245,224.7924652 532.8342896,221.0416718
						526.5026245,224.5632629 					"
                />
              </g>
              <polygon
                fill="#566472"
                points="523.8504028,235.8651733 523.8504028,226.8955994 519.006897,224.124588
					518.7323608,224.3991241 519.006897,233.0899658 523.5653687,236.1401367 				"
              />
              <polygon
                fill="#B6D9D2"
                points="523.5653687,236.1401367 523.5653687,227.1701355 518.7323608,224.3991241
					518.7323608,233.364502 				"
              />
              <path
                fill="#138391"
                d="M521.8068848,226.157608l-3.0745239-1.7584839v4.8292236
					C519.7528687,228.207428,520.7796631,227.1869202,521.8068848,226.157608z"
              />
              <polygon
                fill="#138391"
                points="520.3023682,234.2708282 523.5653687,236.1401367 523.5653687,231.0074005 				"
              />
            </g>
            <g>
              <path
                fill="#1F193B"
                d="M436.9147034,167.3836975c0,2.6933441-17.2322388,2.6933441-17.2322388,0
					c0,0-3.3348083,168.8491211-3.3348083,171.9114685c0,3.7264404,5.3518677,6.7518616,11.9479675,6.7518616
					c6.5982056,0,11.9479675-3.0254211,11.9479675-6.7518616
					C440.2435913,336.23703,436.9147034,167.3836975,436.9147034,167.3836975z"
              />
              <path
                fill="#B6D9D2"
                d="M419.6824646,167.3836975c0-2.6895752,3.8578186-4.8661652,8.6131592-4.8661652
					c4.7574463,0,8.6190796,2.17659,8.6190796,4.8661652c0,2.6933441-3.8616333,4.8699341-8.6190796,4.8699341
					C423.5402832,172.2536316,419.6824646,170.0770416,419.6824646,167.3836975z"
              />
              <path
                fill="#566472"
                d="M422.5235596,167.3836975c0-1.6438904,2.586731-2.9762878,5.7720642-2.9762878
					c3.187439,0,5.7678833,1.3323975,5.7678833,2.9762878c0,1.6476593-2.5804443,2.9842529-5.7678833,2.9842529
					C425.1102905,170.3679504,422.5235596,169.0313568,422.5235596,167.3836975z"
              />
              <path
                fill="#138391"
                d="M428.2956238,197.6620636c4.0152588,0,7.4814453-1.344574,9.1786499-3.2957458
					c-0.0718079-2.8083649-0.1414795-5.5302582-0.2027588-8.0884399c-1.4944458,2.0418396-4.9606323,3.468277-8.9758911,3.468277
					c-4.0135803,0-7.4755554-1.4264374-8.9737549-3.468277c-0.0613098,2.5581818-0.1330872,5.2800751-0.2048645,8.0884399
					C420.8162842,196.3174896,424.2820435,197.6620636,428.2956238,197.6620636z"
              />
              <path
                fill="#566472"
                d="M428.2956238,344.3376465c-6.087738,0-11.0991516-2.5745544-11.8413391-5.9034729
					c-0.0654907,2.0007019-0.1066284,3.2713928-0.1066284,3.5954895c0,3.7264404,5.3518677,6.7518311,11.9479675,6.7518311
					c6.5982056,0,11.9479675-3.0253906,11.9479675-6.7518311c0-0.3240967-0.0348511-1.5947876-0.1066284-3.5954895
					C439.3947754,341.763092,434.3854675,344.3376465,428.2956238,344.3376465z"
              />
              <path
                fill="#138391"
                d="M428.2956238,212.8222504c4.197876,0,7.817688-1.3693542,9.5908813-3.3700562
					c-0.0718079-2.8696594-0.1372681-5.6528473-0.2090759-8.2807159c-1.5641174,2.0947418-5.1839294,3.5501404-9.3818054,3.5501404
					c-4.1978455,0-7.8155823-1.4553986-9.3818054-3.5501404c-0.0654907,2.6278687-0.1309814,5.4110565-0.2027588,8.2807159
					C420.4800415,211.4528961,424.0977783,212.8222504,428.2956238,212.8222504z"
              />
              <path
                fill="#138391"
                d="M428.2956238,341.6031494c-6.087738,0-11.0991516-2.5745544-11.8413391-5.8992615
					c-0.0654907,1.996521-0.1066284,3.2676086-0.1066284,3.5912781c0,3.7264404,5.3518677,6.7518616,11.9479675,6.7518616
					c6.5982056,0,11.9479675-3.0254211,11.9479675-6.7518616c0-0.3236694-0.0348511-1.5947571-0.1066284-3.5912781
					C439.3947754,339.028595,434.3854675,341.6031494,428.2956238,341.6031494z"
              />
            </g>
            <g>
              <path
                fill="#B6D9D2"
                d="M465.0865784,126.8805771c0,2.9065933-18.6158752,2.9065933-18.6158752,0
					c0,0-3.5992432,196.2338867-3.5992432,199.5422363c0,4.0257568,5.7842407,7.2929688,12.9092712,7.2929688
					c7.1246338,0,12.907196-3.2672119,12.907196-7.2929688
					C468.6879272,323.1186829,465.0865784,126.8805771,465.0865784,126.8805771z"
              />
              <path
                fill="#EDEDED"
                d="M446.4707031,126.8805771c0-2.9066086,4.1672363-5.26371,9.3100281-5.26371
					c5.1365051,0,9.3058472,2.3571014,9.3058472,5.26371c0,2.9065933-4.169342,5.2599258-9.3058472,5.2599258
					C450.6379395,132.1405029,446.4707031,129.7871704,446.4707031,126.8805771z"
              />
              <path
                fill="#566472"
                d="M449.5494385,126.8805771c0-1.7790604,2.7857056-3.22229,6.2312927-3.22229
					c3.4456177,0,6.2351074,1.4432297,6.2351074,3.22229c0,1.7790451-2.7894897,3.2180862-6.2351074,3.2180862
					C452.335144,130.0986633,449.5494385,128.6596222,449.5494385,126.8805771z"
              />
              <path
                fill="#138391"
                d="M455.7807312,160.143631c4.3187866,0,8.0490112-1.5166931,9.8733826-3.7180481
					c-0.0759583-3.1689758-0.141449-6.2434845-0.2132568-9.133728c-1.6111145,2.3042145-5.3354797,3.9149323-9.6601257,3.9149323
					c-4.3313599,0-8.0515137-1.6107178-9.6668396-3.9149323c-0.0654907,2.8902435-0.1372681,5.9647522-0.2132568,9.1295319
					C447.7292175,158.6269379,451.4598999,160.143631,455.7807312,160.143631z"
              />
              <path
                fill="#138391"
                d="M455.7807312,177.2386017c4.5030823,0,8.3995361-1.5452271,10.3057861-3.7961273
					c-0.0713806-3.2386475-0.1414795-6.3786621-0.2128601-9.3343811c-1.6871033,2.3571014-5.575592,4.005188-10.092926,4.005188
					c-4.5177307,0-8.4062195-1.6480865-10.092926-4.005188c-0.0717773,2.955719-0.1435547,6.0957336-0.2195435,9.3343811
					C447.3787231,175.6933746,451.2671814,177.2386017,455.7807312,177.2386017z"
              />
              <path
                fill="#566472"
                d="M455.7807312,331.2520447c-6.5776367,0-11.9953918-2.7835999-12.7967834-6.3748779
					c-0.0717773,2.1522522-0.1124878,3.5299988-0.1124878,3.878418c0,4.029541,5.7842407,7.2967224,12.9092712,7.2967224
					c7.1246338,0,12.907196-3.2671814,12.907196-7.2967224c0-0.3484192-0.039032-1.7261658-0.1108398-3.878418
					C467.7736206,328.4684448,462.3562927,331.2520447,455.7807312,331.2520447z"
              />
              <path
                fill="#138391"
                d="M455.7807312,328.9155273c-6.5776367,0-11.9953918-2.7798462-12.7967834-6.370697
					c-0.0717773,2.1522522-0.1124878,3.5211792-0.1124878,3.8779907c0,4.0257568,5.7842407,7.2929688,12.9092712,7.2929688
					c7.1246338,0,12.907196-3.2672119,12.907196-7.2929688c0-0.3568115-0.039032-1.7257385-0.1108398-3.8779907
					C467.7736206,326.1356812,462.3562927,328.9155273,455.7807312,328.9155273z"
              />
            </g>
          </g>
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M524.2420654,53.8729477c0.4202271-1.5947685,0.6641235-3.2508278,0.6641235-4.9727936
			c0-10.7364616-8.7055664-19.4436779-19.4441223-19.4436779c-7.5611877,0-14.1022949,4.3292599-17.3140869,10.6424313
			c-2.9435425-0.7627525-6.0117798-1.213604-9.1929016-1.213604c-16.26297,0-30.0248413,10.634037-34.7575073,25.3185997
			c-7.4243469,4.6978302-12.3702698,12.9621735-12.3702698,22.3993988c0,6.9730759,2.7097168,13.2946396,7.1107483,18.0294189
			c-0.010498,0.2707596-0.0411377,0.5410995-0.0411377,0.8198395c0,10.0887375,8.1782837,18.2674408,18.2611389,18.2674408
			c7.9222107,0,14.6455383-5.0504532,17.1768494-12.1058121c1.5187988,0.1884842,3.0539551,0.3240738,4.6201782,0.3240738
			c7.6271057,0,14.7026062-2.3411484,20.56073-6.3421326c2.9682922,1.0330963,6.1595459,1.6275101,9.4842529,1.6275101
			c15.942627,0,28.8666382-12.9298477,28.8666382-28.868309C537.8666992,68.0164032,532.4119873,58.9687386,524.2420654,53.8729477z
			"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M520.9929199,65.7697067c0.3463135-1.3160324,0.5494995-2.6849594,0.5494995-4.1118126
			c0-8.8793297-7.1968384-16.0740547-16.0803528-16.0740547c-6.2493591,0-11.6566162,3.5828819-14.3214417,8.7932739
			c-2.4288635-0.6271629-4.964386-0.9999313-7.5922546-0.9999313c-13.4461975,0-24.8303833,8.7932701-28.7436218,20.9360199
			c-6.1389771,3.8859634-10.2302246,10.7200928-10.2302246,18.5214081c0,5.7636719,2.2404175,10.9946365,5.8786926,14.9057922
			c-0.0100708,0.2292023-0.0285339,0.454628-0.0285339,0.6804733c0,8.346199,6.7577209,15.1064453,15.0984802,15.1064453
			c6.5507507,0,12.1095886-4.1772995,14.2047424-10.0110779c1.2501221,0.1557388,2.5187073,0.2665634,3.8204651,0.2665634
			c6.3031006,0,12.1570435-1.9389954,17.0005188-5.2431335c2.4536438,0.8525848,5.0874023,1.344574,7.8403625,1.344574
			c13.1838074,0,23.8711853-10.6915436,23.8711853-23.8673935C532.260437,77.4616013,527.7468872,69.9881439,520.9929199,65.7697067
			z M508.3892517,108.4208755c-2.4801025,0-4.9501343-0.426506-7.3626404-1.2627182l-0.6968384-0.2417984l-0.6091309,0.4181061
			c-4.7838745,3.2588043-10.377533,4.9849701-16.1722717,4.9849701c-1.1069641,0-2.2609558-0.082283-3.6445923-0.2585907
			l-1.1619568-0.1473465l-0.3975525,1.1069794c-1.9309998,5.4068527-7.0881042,9.0434647-12.821106,9.0434647
			c-7.522583,0-13.6409912-6.120491-13.6409912-13.6430664c0-0.1108246,0.0058899-0.217453,0.0163879-0.3282776l0.0386047-0.9017029
			l-0.4159851-0.4470673c-3.5379639-3.8041077-5.4891357-8.7441635-5.4891357-13.9092178
			c0-7.0431824,3.5727844-13.5036926,9.5559998-17.2876511l0.4466553-0.2787399l0.1620483-0.5083618
			c3.836853-11.9131279,14.8298035-19.9234962,27.3516235-19.9234962c2.3592224,0,4.7880859,0.3198776,7.2316589,0.9554329
			l1.1334229,0.2951088l0.5348206-1.0456886c2.5149231-4.9354324,7.4999084-8.0019722,13.0137939-8.0019722
			c8.061615,0,14.6169739,6.559166,14.6169739,14.6186523c0,1.2178001-0.1620483,2.472126-0.498291,3.7386208l-0.2745361,1.0414886
			l0.9138794,0.5696487c6.6229858,4.1365814,10.5769653,11.2368546,10.5769653,19.009201
			C530.7970581,98.36866,520.744812,108.4208755,508.3892517,108.4208755z"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M468.1304321,129.8652496c0.233429-0.8979187,0.3748779-1.8243866,0.3748779-2.7919922
			c0-6.0180664-4.8783264-10.9006042-10.9006042-10.9006042c-4.2427673,0-7.903717,2.4272079-9.70755,5.9731445
			c-1.6518555-0.4344788-3.3696289-0.6888657-5.1466064-0.6888657c-9.1173096,0-16.8388672,5.960968-19.4848022,14.1967697
			c-4.1672058,2.6358337-6.9428406,7.2639771-6.9428406,12.5566559c0,3.9065399,1.5250854,7.456665,3.9888,10.1009064
			c-0.0037842,0.1599426-0.024353,0.3114777-0.024353,0.4634399c0,5.6570435,4.583252,10.2402802,10.2344055,10.2402802
			c4.4417725,0,8.2131348-2.8369141,9.6357727-6.7887878c0.8446045,0.1108246,1.7072754,0.1889038,2.5930176,0.1889038
			c4.2675781,0,8.237915-1.31604,11.5193787-3.5585327c1.6686401,0.5780487,3.4519043,0.9100952,5.3170166,0.9100952
			c8.9389343,0,16.1806946-7.2438354,16.1806946-16.1764832
			C475.7676392,137.7933502,472.7136841,132.7181244,468.1304321,129.8652496z M459.591156,158.774704
			c-1.6871338,0-3.3637695-0.2829285-4.9891663-0.8525848l-0.4777222-0.1641388l-0.4118042,0.2787476
			c-3.238678,2.2135315-7.03479,3.3822174-10.9619141,3.3822174c-0.7522583,0-1.535553-0.0533142-2.4700012-0.1725311
			l-0.793396-0.1024323l-0.2644653,0.7463837c-1.3118591,3.6689301-4.8086548,6.1326599-8.7009277,6.1326599
			c-5.0915833,0-9.2382507-4.1487427-9.2382507-9.2483215c0-0.0696869,0-0.1519623,0.0101013-0.2216492l0.0310364-0.6107788
			l-0.2850342-0.2993164c-2.4003296-2.5825195-3.7201233-5.9315796-3.7201233-9.4326019
			c0-4.7717133,2.4183655-9.1542969,6.474762-11.7204437l0.2993164-0.192688l0.1108398-0.3404388
			c2.6030884-8.0758591,10.053894-13.5037003,18.5461426-13.5037003c1.5943604,0,3.2424316,0.2174454,4.8989258,0.647728
			l0.7723999,0.1968765l0.3547363-0.7052383c1.7072754-3.3490601,5.093689-5.4274216,8.8280945-5.4274216
			c5.462677,0,9.9107666,4.4438629,9.9107666,9.908226c0,0.8240356-0.1171265,1.6808167-0.3404541,2.5376129l-0.1842957,0.7052307
			l0.6149902,0.385376c4.492981,2.7999725,7.1720581,7.6166077,7.1720581,12.8887024
			C474.777771,151.9611511,467.9663086,158.774704,459.591156,158.774704z"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M568.8565674,127.1143951c0.192688-0.705246,0.2993164-1.4432297,0.2993164-2.2097549
			c0-4.7801132-3.8657837-8.6497116-8.6480103-8.6497116c-3.3654175,0-6.2783203,1.9268265-7.7047729,4.7305832
			c-1.3059692-0.3362503-2.6790771-0.5411072-4.0912476-0.5411072c-7.2379761,0-13.3605347,4.7347717-15.465332,11.2695999
			c-3.3041382,2.0867462-5.5038452,5.7636719-5.5038452,9.9657288c0,3.0992889,1.2094116,5.9156342,3.1610107,8.0187683
			c0,0.1230011-0.0205688,0.2459869-0.0205688,0.3647919c0,4.492981,3.6382446,8.1291656,8.1312256,8.1291656
			c3.5274658,0,6.5159302-2.2466888,7.6434937-5.3867035c0.6704102,0.0860596,1.3529663,0.1393738,2.0540161,0.1393738
			c3.3944092,0,6.5444946-1.0372925,9.1480103-2.8205414c1.3198242,0.4676361,2.7382813,0.730011,4.222229,0.730011
			c7.09021,0,12.84375-5.7519226,12.84375-12.8437958C574.9258423,133.4069977,572.494873,129.385437,568.8565674,127.1143951z
			 M562.0820923,150.0632935c-1.3361816,0-2.6686401-0.2216492-3.9581909-0.680481l-0.3769531-0.127182l-0.3299561,0.2254181
			c-2.5766602,1.7589111-5.5856934,2.6811829-8.7051392,2.6811829c-0.5906372,0-1.2094116-0.0453339-1.9616699-0.1351776
			l-0.6212769-0.0780792l-0.2132568,0.5902252c-1.0473633,2.9107971-3.8204956,4.8703613-6.9013062,4.8703613
			c-4.0500488,0-7.3441162-3.2961578-7.3441162-7.3462677c0-0.0533142,0.0062866-0.1150208,0.0100708-0.1763153l0.0268555-0.4835815
			l-0.2233276-0.2380219c-1.90625-2.0536041-2.9578247-4.710434-2.9578247-7.4856415
			c0-3.7919312,1.9205322-7.2681885,5.1428223-9.3016357l0.2438965-0.1557312l0.0818481-0.2707672
			c2.0682983-6.4114075,7.9776001-10.7158966,14.7172852-10.7158966c1.2706909,0,2.5804443,0.1721115,3.8922729,0.512558
			l0.6107788,0.1557388l0.2892456-0.5574722c1.3504639-2.6568298,4.0358276-4.3044968,7.0037231-4.3044968
			c4.3330078,0,7.8609009,3.5253677,7.8609009,7.8626099c0,0.6561203-0.0923462,1.3282013-0.2686768,2.0128708l-0.1473389,0.5574799
			l0.4915771,0.3035049c3.5627441,2.234108,5.6986084,6.0508041,5.6986084,10.2323074
			C574.1429443,144.6560211,568.7293701,150.0632935,562.0820923,150.0632935z"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M560.8172607,83.8117142c0.2543945-0.9797821,0.4059448-1.9885254,0.4059448-3.0421906
			c0-6.5549698-5.3149414-11.8677902-11.8720093-11.8677902c-4.6138916,0-8.6090088,2.6442337-10.5706787,6.4974594
			c-1.7996216-0.4672241-3.6688843-0.7417679-5.6099854-0.7417679c-9.9309082,0-18.3308716,6.4894867-21.2189636,15.4548721
			c-4.527832,2.8696594-7.5511475,7.9121399-7.5511475,13.6758118c0,4.2595749,1.651886,8.1169968,4.3393555,11.0030289
			c-0.0041809,0.168335-0.0247803,0.3362503-0.0247803,0.5003815c0,6.1616287,4.9912415,11.1503754,11.1507874,11.1503754
			c4.8330078,0,8.9389648-3.082489,10.4841919-7.3911819c0.9306641,0.1187973,1.8655396,0.2006607,2.8205566,0.2006607
			c4.6549683,0,8.9737549-1.4306335,12.5524292-3.8695984c1.8143311,0.6313629,3.7554321,0.9877625,5.7926636,0.9877625
			c9.7281494,0,17.6154785-7.8873749,17.6154785-17.6197128C569.1311035,92.4408493,565.8021851,86.9189758,560.8172607,83.8117142z
			 M551.515625,115.2915192c-1.8386841,0-3.6588745-0.3077011-5.4379272-0.9348602l-0.5163574-0.1763153l-0.4491577,0.3114853
			c-3.5253906,2.4066391-7.6577148,3.67733-11.9416504,3.67733c-0.8135376,0-1.6665649-0.0570908-2.6832886-0.1884842
			l-0.8647461-0.1108246l-0.2892456,0.8198471c-1.4285278,3.9930115-5.2389526,6.6783829-9.4674683,6.6783829
			c-5.5550537,0-10.0723267-4.5219421-10.0723267-10.076561c0-0.0780792,0.0100403-0.159935,0.0142517-0.2417908
			l0.0306396-0.6683044l-0.3093567-0.3240738c-2.6136169-2.8163528-4.0463562-6.4567413-4.0463562-10.2692413
			c0-5.1977997,2.6337585-9.9737167,7.0448914-12.7615204l0.3299561-0.209053l0.1271973-0.3769684
			c2.8305664-8.7974701,10.9454956-14.7051239,20.1858521-14.7051239c1.7383423,0,3.5379639,0.2380142,5.3417969,0.7052383
			l0.8383179,0.217453l0.3912354-0.7707291c1.8550415-3.6445923,5.5386353-5.9076614,9.609314-5.9076614
			c5.9525757,0,10.7939453,4.8418198,10.7939453,10.7897797c0,0.8937225-0.1271973,1.8243942-0.3752441,2.7592545
			l-0.2027588,0.7745056l0.6762695,0.4143295c4.8930054,3.0539551,7.8096924,8.3012848,7.8096924,14.0322113
			C568.0531006,107.8713684,560.6329346,115.2915192,551.515625,115.2915192z"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M489.556366,105.6166992c0.3647766-1.3978882,0.5881042-2.8574905,0.5881042-4.3657837
			c0-9.4372253-7.6535339-17.086998-17.0844727-17.086998c-6.6473083,0-12.3908386,3.8087234-15.2172546,9.3469696
			c-2.5909424-0.672493-5.2842712-1.065834-8.0800781-1.065834c-14.2849121,0-26.3802185,9.3469696-30.5369263,22.2478561
			c-6.5264282,4.1281815-10.8720703,11.3883896-10.8720703,19.681694c0,6.1246948,2.3839722,11.6876984,6.245575,15.8444366
			c-0.0100708,0.2375946-0.0348206,0.4756165-0.0348206,0.7253876c0,8.8591766,7.1863098,16.0455017,16.0492859,16.0455017
			c6.9570923,0,12.8685303-4.4438629,15.0942383-10.6382294c1.3303223,0.168335,2.677002,0.2829285,4.054718,0.2829285
			c6.7044373,0,12.9193726-2.057785,18.0663757-5.5751801c2.6173706,0.9180756,5.413147,1.4348297,8.3361206,1.4348297
			c14.0099487,0,25.3635254-11.359848,25.3635254-25.3635101
			C501.5286865,118.0423813,496.7321777,110.0975037,489.556366,105.6166992z"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M574.8582764,72.5379257c0.3299561-1.2463455,0.5247192-2.5455856,0.5247192-3.9027557
			c0-8.4364548-6.8421021-15.2705841-15.272644-15.2705841c-5.9400024,0-11.0828247,3.4027939-13.6040649,8.3587952
			c-2.3079834-0.5981979-4.7225952-0.9550171-7.2174072-0.9550171c-12.7757568,0-23.5861206,8.354599-27.3066711,19.8907547
			c-5.8291626,3.6895065-9.7155457,10.1789856-9.7155457,17.5949402c0,5.4727554,2.1295776,10.4371567,5.5856934,14.1594086
			c-0.010498,0.2132492-0.0310669,0.4264984-0.0310669,0.647728c0,7.9243164,6.4177246,14.3441086,14.34198,14.3441086
			c6.2250366,0,11.5055542-3.972435,13.4915161-9.5106812c1.1951904,0.1515427,2.4003906,0.2539673,3.6340942,0.2539673
			c5.9874268,0,11.5462646-1.8403473,16.145874-4.9807663c2.3428345,0.8160629,4.8375854,1.2748871,7.4508057,1.2748871
			c12.5280762,0,22.6764526-10.1462402,22.6764526-22.6739349C585.5620117,83.647583,581.2780762,76.5431137,574.8582764,72.5379257
			z"
          />
          <path
            opacity="0.8"
            fill="#ECE7E1"
            d="M574.8788452,118.9319153c0.2333984-0.8773575,0.3706665-1.8038254,0.3706665-2.7592621
			c0-5.9605484-4.8330078-10.7935562-10.7939453-10.7935562c-4.2016602,0-7.8361816,2.4020233-9.6210938,5.911438
			c-1.6296387-0.4307022-3.3373413-0.6766968-5.1021118-0.6766968c-9.0308838,0-16.668457,5.9034576-19.2963257,14.0573959
			c-4.1260986,2.6068802-6.8706665,7.1985016-6.8706665,12.43367c0,3.8737793,1.5045166,7.3832092,3.9481201,10.0106506
			c-0.0042114,0.1557312-0.0247803,0.3076935-0.0247803,0.4592438c0,5.5999603,4.5441895,10.1378479,10.144165,10.1378479
			c4.4005737,0,8.1312256-2.8037567,9.5333252-6.718689c0.8446045,0.1066284,1.6972046,0.1800995,2.5661621,0.1800995
			c4.2348022,0,8.1682129-1.3034515,11.4152832-3.5211792c1.6518555,0.5738525,3.4208374,0.9017029,5.2695313,0.9017029
			c8.8529053,0,16.0291748-7.1741486,16.0291748-16.029129C582.4463501,126.7903214,579.416748,121.7684097,574.8788452,118.9319153
			z"
          />
          <g>
            <g>
              <path
                fill="#DBDBDB"
                d="M474.27948,329.2639465c0-0.0205688,0-2.3818665,0-2.4024353
					c0.0512085-1.0578613,1.5842896-0.0575256,3.4598694-0.0575256c1.7937622,0,3.2835693-0.8483887,3.4620056,0.1393738
					c0,0,0,2.2546692,0,2.3205872c0,1.0822144-1.5452576,1.9553528-3.4620056,1.9553528
					C475.8251343,331.2192993,474.27948,330.3461609,474.27948,329.2639465z"
              />
              <path
                fill="#F2D323"
                d="M474.27948,326.8615112c0-1.0780334,1.5456543-1.9553833,3.4598694-1.9553833
					c1.916748,0,3.4620056,0.8773499,3.4620056,1.9553833c0,1.0822144-1.5452576,1.9553528-3.4620056,1.9553528
					C475.8251343,328.816864,474.27948,327.9437256,474.27948,326.8615112z"
              />
            </g>
            <g>
              <path
                fill="#DBDBDB"
                d="M436.0415344,350.8884277c0-0.0163879,0-2.381897,0-2.3982544
					c0.0512085-1.0616455,1.5846863-0.0612793,3.4661865-0.0612793c1.7895508,0,3.2776794-0.8488159,3.4556885,0.1393433
					c0,0,0,2.2546997,0,2.3201904c0.0041809,1.0821838-1.5452576,1.9553528-3.4556885,1.9553528
					C437.5913696,352.8437805,436.0415344,351.9706116,436.0415344,350.8884277z"
              />
              <path
                fill="#F2D323"
                d="M436.0415344,348.4901733c0-1.0863953,1.5498352-1.9633484,3.4661865-1.9633484
					c1.9104309,0,3.4556885,0.8769531,3.4556885,1.9633484c0,1.0784302-1.5452576,1.9515991-3.4556885,1.9515991
					C437.5913696,350.4417725,436.0415344,349.5686035,436.0415344,348.4901733z"
              />
            </g>
            <g>
              <path
                fill="#ECE7E1"
                d="M441.885376,348.1543274c0,1.1435242-4.5731506,1.1435242-4.5731506,0
					c0-9.3754883,0-44.9469604,0-44.9469604c0-6.3828735,4.9299622-14.1921692,11.2162781-17.7750244l17.8636169-10.1668396
					c3.6424866-2.0783386,7.2417297-2.3860474,9.8796692-0.856781c2.3222656,1.3525696,3.5971375,3.902771,3.5971375,7.1741638
					c0,0,0,33.2219238,0,44.9423523c0,1.1439209-4.5727234,1.1439209-4.5727234,0c0-9.8385315,0-44.9423523,0-44.9423523
					c0-1.1153564-0.2296143-2.5787354-1.3223267-3.2142944c-1.1783447-0.680481-3.1605835-0.3564148-5.3149109,0.8693542
					l-17.8631897,10.1664124c-4.8334351,2.7550659-8.9104004,9.0724182-8.9104004,13.803009
					C441.885376,303.2073669,441.885376,337.9300232,441.885376,348.1543274z"
              />
            </g>
          </g>
          <g>
            <g>
              <path
                fill="#DBDBDB"
                d="M493.9775391,331.506012c0-0.0201416,0-2.3856506,0-2.4020081
					c0.051239-1.0578613,1.5842896-0.0617065,3.4556885-0.0617065c1.7996216,0,3.2839966-0.8442078,3.4661865,0.1393738
					c0,0,0,2.2588501,0,2.3243408c0,1.0784302-1.5494385,1.9558105-3.4661865,1.9558105
					C495.5227966,333.4618225,493.9775391,332.5844421,493.9775391,331.506012z"
              />
              <path
                fill="#F2D323"
                d="M493.9775391,329.1040039c0-1.0863953,1.5452576-1.9595642,3.4556885-1.9595642
					c1.916748,0,3.4661865,0.8731689,3.4661865,1.9595642c0,1.0822144-1.5494385,1.9553528-3.4661865,1.9553528
					C495.5227966,331.0593567,493.9775391,330.1778259,493.9775391,329.1040039z"
              />
            </g>
            <g>
              <path
                fill="#DBDBDB"
                d="M464.0022583,348.6828613c0-0.0201721,0-2.3898621,0-2.4104309
					c0.051239-1.049469,1.5863647-0.0533142,3.4619751-0.0533142c1.793335,0,3.2772827-0.8483887,3.4598999,0.1351929
					c0,0,0.0058594,2.2588501,0.0058594,2.3285522c0,1.0784302-1.549408,1.95578-3.4657593,1.95578
					C465.5474854,350.6386414,464.0022583,349.7612915,464.0022583,348.6828613z"
              />
              <path
                fill="#F2D323"
                d="M464.0022583,346.2724304c0-1.0737915,1.5452271-1.9553528,3.4619751-1.9553528
					c1.9163513,0.0041809,3.4657593,0.8815613,3.4657593,1.9553528c0,1.0822144-1.549408,1.9637756-3.4657593,1.9637756
					C465.5474854,348.2362061,464.0022583,347.3546448,464.0022583,346.2724304z"
              />
            </g>
            <g>
              <path
                fill="#138391"
                d="M469.1962891,346.1208801c0,0.9059143-3.607666,0.9059143-3.607666,0
					c0-7.3873901,0-35.4400635,0-35.4400635c0-5.038269,3.8885193-11.1957092,8.8424072-14.024231l14.0880432-8.0187683
					c2.8755188-1.635498,5.7166443-1.8814697,7.7950134-0.6720581c1.8306885,1.0658264,2.8411255,3.0744934,2.8411255,5.6570435
					c0,0,0,26.2039185,0,35.4442444c0,0.9059143-3.6034546,0.9059143-3.6034546,0c0-7.7601624,0-35.4442444,0-35.4442444
					c0-0.881134-0.1826172-2.0372314-1.0452881-2.5414124c-0.9264526-0.5331116-2.4968872-0.2791443-4.1978455,0.6926575
					l-14.0880432,8.0187683c-3.8125,2.1723938-7.024292,7.1493835-7.024292,10.8880005
					C469.1962891,310.6808167,469.1962891,338.0614014,469.1962891,346.1208801z"
              />
            </g>
          </g>
          <g>
            <g>
              <path
                fill="#DBDBDB"
                d="M569.3342896,302.4735718c0-0.0285339,0-3.1072693,0-3.132019
					c-0.0717773-1.3731384-2.0623779-0.0697021-4.5076294-0.0697021c-2.3323364,0-4.2696533-1.1107483-4.4971924,0.1763306
					c0,0-0.0058594,2.9351501-0.0058594,3.0253906c0,1.4020996,2.0187378,2.5460205,4.5030518,2.5460205
					C567.3109131,305.0195923,569.3342896,303.8756714,569.3342896,302.4735718z"
              />
              <path
                fill="#F2D323"
                d="M569.3342896,299.3415527c0-1.4020996-2.0233765-2.5413818-4.5076294-2.5413818
					c-2.484314,0-4.5030518,1.1392822-4.5030518,2.5455627c0,1.4020996,2.0187378,2.5460205,4.5030518,2.5460205
					C567.3109131,301.8917542,569.3342896,300.7478333,569.3342896,299.3415527z"
              />
            </g>
            <g>
              <path
                fill="#DBDBDB"
                d="M608.3345947,324.8238525c0-0.024353,0-3.1072693,0-3.132019
					c-0.0654907-1.3731384-2.0582275-0.0776672-4.503479-0.0776672c-2.3365479,0-4.2675781-1.1027832-4.5009766,0.1842957
					c0,0-0.0062866,2.931366-0.0062866,3.0253906c0,1.4020996,2.0170898,2.5418091,4.5072632,2.5418091
					C606.3217163,327.3656616,608.3345947,326.2259521,608.3345947,324.8238525z"
              />
              <path
                fill="#F2D323"
                d="M608.3345947,321.6918335c0-1.4016724-2.0128784-2.5455933-4.503479-2.5455933
					c-2.4901733,0-4.5072632,1.1439209-4.5072632,2.5497742c0,1.4020996,2.0170898,2.5418091,4.5072632,2.5418091
					C606.3217163,324.2378235,608.3345947,323.098114,608.3345947,321.6918335z"
              />
            </g>
            <g>
              <path
                fill="#D35400"
                d="M601.5743408,321.4991455c0,1.1724548,4.696167,1.1724548,4.696167,0
					c0-9.6252747,0-46.1311951,0-46.1311951c0-6.5512085-5.0567627-14.5653381-11.5093384-18.2384644l-18.3328857-10.4375916
					c-3.7386475-2.1316681-7.4344482-2.4511261-10.1420288-0.8769379c-2.3798218,1.3852997-3.6895142,4.0009918-3.6895142,7.3626404
					c0,0,0,34.0950775,0,46.1232147c0,1.1724854,4.6898193,1.1724854,4.6898193,0c0-10.0971069,0-46.1232147,0-46.1232147
					c0-1.1439209,0.2397461-2.6484375,1.3609619-3.3045502c1.2052002-0.6968536,3.2386475-0.360611,5.4542847,0.8937225
					l18.335022,10.4375763c4.9559937,2.8201294,9.1375122,9.3096008,9.1375122,14.1636047
					C601.5743408,275.3679504,601.5743408,311.0044861,601.5743408,321.4991455z"
              />
            </g>
          </g>
          <g>
            <g>
              <path
                fill="#DBDBDB"
                d="M555.815918,311.2303162c0-0.0205688,0-2.7097168,0-2.7302856
					c-0.0549927-1.201416-1.7996216-0.065918-3.9334106-0.065918c-2.03302,0-3.7302246-0.9671631-3.9291992,0.1561584
					c0,0-0.0042114,2.5661621-0.0042114,2.6400452c0,1.2295532,1.7589111,2.2177429,3.9334106,2.2177429
					C554.0574341,313.4480591,555.815918,312.4598694,555.815918,311.2303162z"
              />
              <path
                fill="#F2D323"
                d="M555.815918,308.5000305c0-1.2299805-1.7584839-2.2219238-3.9334106-2.2219238
					c-2.1744995,0-3.9291992,0.9919434-3.9334106,2.2219238c0,1.2295532,1.7589111,2.225708,3.9334106,2.225708
					C554.0574341,310.7257385,555.815918,309.7257996,555.815918,308.5000305z"
              />
            </g>
            <g>
              <path
                fill="#DBDBDB"
                d="M589.8665161,330.739502c0-0.0163879,0-2.7097473,0-2.7303162
					c-0.0554199-1.1925964-1.7996216-0.0654602-3.9296265-0.0654602c-2.0372314,0-3.7302246-0.9592285-3.9334106,0.1557312
					c0,0-0.0042114,2.5623779-0.0042114,2.6400452c0,1.22995,1.7631226,2.2261047,3.9376221,2.2261047
					C588.107605,332.9656067,589.8665161,331.9694519,589.8665161,330.739502z"
              />
              <path
                fill="#F2D323"
                d="M589.8665161,328.0091858c0-1.225769-1.7589111-2.2219238-3.9296265-2.2219238
					c-2.1744995,0-3.9376221,0.9961548-3.9376221,2.2219238c0,1.2299805,1.7631226,2.2261353,3.9376221,2.2261353
					C588.107605,330.235321,589.8665161,329.2391663,589.8665161,328.0091858z"
              />
            </g>
            <g>
              <path
                fill="#C1382A"
                d="M583.9714355,327.8370972c0,1.025116,4.095459,1.025116,4.095459,0
					c0-8.3999329,0-40.2693176,0-40.2693176c0-5.71875-4.4153442-12.7165833-10.0522461-15.930481l-16.0043945-9.1047363
					c-3.2608643-1.8613586-6.4894409-2.1400757-8.8465576-0.7665405c-2.0846558,1.2089844-3.2323608,3.4926147-3.2323608,6.4277649
					c0,0,0,29.7662354,0,40.2651062c0,1.0246887,4.1013184,1.0246887,4.1013184,0c0-8.8138428,0-40.2651062,0-40.2651062
					c0-1.0003357,0.203186-2.3121948,1.1888428-2.8860168c1.0477905-0.6108093,2.8310547-0.3198853,4.7616577,0.7828979
					l15.9984741,9.109375c4.3309326,2.4674988,7.9898071,8.1333618,7.9898071,12.3677368
					C583.9714355,287.5677795,583.9714355,318.6790161,583.9714355,327.8370972z"
              />
            </g>
          </g>
          <g>
            <g>
              <path
                fill="#DBDBDB"
                d="M538.7192383,320.6755371c0-0.0205688,0-3.1118774,0-3.1362305
					c-0.0696411-1.3735657-2.0619507-0.0697021-4.5114136-0.0697021c-2.3428345,0-4.2839355-1.1149597-4.5135498,0.1763306
					c0,0-0.010498,2.9435425-0.010498,3.0296021c0,1.4142456,2.0275879,2.5497742,4.5240479,2.5497742
					C536.697998,323.2253113,538.7192383,322.0897827,538.7192383,320.6755371z"
              />
              <path
                fill="#F2D323"
                d="M538.7192383,317.5393066c0-1.4062805-2.0212402-2.5498047-4.5114136-2.5498047
					c-2.49646,0-4.5240479,1.1435242-4.5240479,2.5498047c0,1.4142456,2.0275879,2.5581665,4.5240479,2.5581665
					C536.697998,320.0974731,538.7192383,318.9535522,538.7192383,317.5393066z"
              />
            </g>
            <g>
              <path
                fill="#DBDBDB"
                d="M577.8118896,343.0791321c0-0.0289917,0-3.1114807,0-3.1320496
					c-0.0717773-1.3815002-2.0640869-0.0822754-4.5135498-0.0822754c-2.3365479,0-4.2839355-1.1065369-4.5135498,0.1805115
					c0,0-0.0042114,2.9477539-0.0042114,3.0338135c0,1.4058533,2.0233765,2.5497742,4.5177612,2.5497742
					C575.7889404,345.6289063,577.8118896,344.4849854,577.8118896,343.0791321z"
              />
              <path
                fill="#F2D323"
                d="M577.8118896,339.9470825c0-1.4142456-2.0229492-2.5581665-4.5135498-2.5581665
					c-2.4943848,0-4.5177612,1.1439209-4.5177612,2.5581665c0,1.4058838,2.0233765,2.5498047,4.5177612,2.5498047
					C575.7889404,342.4968872,577.8118896,341.3529663,577.8118896,339.9470825z"
              />
            </g>
            <g>
              <path
                fill="#B6D9D2"
                d="M571.0373535,339.7422485c0,1.1804199,4.7062378,1.1804199,4.7062378,0
					c0-9.6463013,0-46.2260742,0-46.2260742c0-6.5713501-5.0668335-14.6064758-11.5420532-18.2875977l-18.3719482-10.4535217
					c-3.744873-2.1400757-7.4508057-2.4599609-10.1583862-0.8815613c-2.3898926,1.3857117-3.710083,4.005188-3.710083,7.3748169
					c0,0,0,34.1773376,0,46.2298584c0,1.1766663,4.7062378,1.1766663,4.7062378,0c0-10.1214905,0-46.2298584,0-46.2298584
					c0-1.1477051,0.2379761-2.6522217,1.3609619-3.3083496c1.2093506-0.6968384,3.2528687-0.3647766,5.4727173,0.8937378
					l18.3719482,10.4661255c4.9707031,2.8285217,9.1643677,9.3305969,9.1643677,14.1963501
					C571.0373535,293.5161743,571.0373535,329.2307739,571.0373535,339.7422485z"
              />
            </g>
          </g>
          <g>
            <g>
              <path
                fill="#DBDBDB"
                d="M518.543457,337.2537537c0-0.0247803,0-2.394043,0-2.4104309
					c0.0512085-1.0536499,1.586792-0.0574951,3.4661865-0.0574951c1.7996216,0,3.2940674-0.8526001,3.4661865,0.1351624
					c0,0,0,2.2630615,0,2.3327637c0,1.0821838-1.5456543,1.9553528-3.4661865,1.9553528
					C520.0991821,339.2091064,518.543457,338.3317566,518.543457,337.2537537z"
              />
              <path
                fill="#F2D323"
                d="M518.543457,334.8433228c0-1.0863953,1.5557251-1.9595642,3.4661865-1.9595642
					c1.9205322,0,3.4661865,0.8731689,3.4661865,1.9595642c0,1.0822144-1.5456543,1.9633484-3.4661865,1.9633484
					C520.0991821,336.8066711,518.543457,335.9255371,518.543457,334.8433228z"
              />
            </g>
            <g>
              <path
                fill="#DBDBDB"
                d="M480.2358398,358.919342c0-0.0205688-0.0058899-2.3898315,0-2.4104004
					c0.0453186-1.0578613,1.5805054-0.0617065,3.4661865-0.0617065c1.7937317,0,3.2818909-0.8483887,3.4640808,0.1393738
					c0,0,0,2.2588501,0,2.3327332c0,1.0822144-1.5536499,1.9595642-3.4640808,1.9595642
					C481.7793884,360.8789063,480.2358398,360.0015564,480.2358398,358.919342z"
              />
              <path
                fill="#F2D323"
                d="M480.2358398,356.5089417c0-1.0822144,1.5435486-1.9595642,3.4661865-1.9595642
					c1.9104309,0,3.4640808,0.8773499,3.4640808,1.9595642s-1.5536499,1.9637451-3.4640808,1.9637451
					C481.7793884,358.4726868,480.2358398,357.591156,480.2358398,356.5089417z"
              />
            </g>
            <g>
              <path
                fill="#1F193B"
                d="M486.0838928,356.172699c0,1.151886-4.583252,1.151886-4.583252,0
					c0-9.3960876,0-45.0326233,0-45.0326233c0-6.3954468,4.9400635-14.2211304,11.2368774-17.8124084l17.8980103-10.1869507
					c3.6550598-2.0787964,7.2580872-2.3902588,9.900238-0.856781c2.3285522,1.3567505,3.6097412,3.9107361,3.6097412,7.1863098
					c0,0,0,33.2958069,0,45.0326233c0,1.1519165-4.5853271,1.1519165-4.5853271,0c0-9.8591003,0-45.0326233,0-45.0326233
					c0-1.1153564-0.2233276-2.5787354-1.3218994-3.2222595c-1.178772-0.680481-3.1710815-0.3526306-5.3295898,0.8689575
					l-17.8938599,10.1915588c-4.8397217,2.7588501-8.9309387,9.0925903-8.9309387,13.8315735
					C486.0838928,311.1400757,486.0838928,345.9324036,486.0838928,356.172699z"
              />
            </g>
          </g>
          <g>
            <g>
              <g>
                <path
                  fill="#566472"
                  d="M529.4197388,347.3916016c0-0.0738831-0.0042114-27.253418,0-27.3352661
						c0.2233276-4.6487122,6.9730835-0.2581787,15.2558594-0.2581787c7.9037476,0,14.4755249-3.7470093,15.2521362,0.602417
						c0,0,0.0146484,26.6958923,0.0146484,26.9910278c0,4.7679138-6.8341064,8.6295471-15.2667847,8.6295471
						C536.2492065,356.0211487,529.4197388,352.1595154,529.4197388,347.3916016z"
                />
                <path
                  fill="#C1382A"
                  d="M529.4197388,342.7961731c0-0.078064-0.0042114-27.2575684,0-27.339447
						c0.2233276-4.6571045,6.9730835-0.2665405,15.2558594-0.2665405c7.9037476,0,14.4755249-3.7386475,15.2521362,0.6107788
						c0,0,0.0146484,26.6959229,0.0146484,26.9952087c0,4.7675171-6.8341064,8.6253662-15.2667847,8.6253662
						C536.2492065,351.4215393,529.4197388,347.5636902,529.4197388,342.7961731z"
                />
                <path
                  fill="#D35400"
                  d="M529.4197388,315.4567261c0-4.7637329,6.8294678-8.6253357,15.2558594-8.6253357
						c8.4326782,0,15.2667847,3.8616028,15.2667847,8.6253357c0,4.7637634-6.8341064,8.6295776-15.2667847,8.6295776
						C536.2492065,324.0863037,529.4197388,320.2204895,529.4197388,315.4567261z"
                />
              </g>
            </g>
          </g>
          <g>
            <g>
              <g>
                <path
                  fill="#566472"
                  d="M500.5388184,361.8339233c0-0.0776672-0.0062866-27.2571716,0-27.3390198
						c0.2275085-4.6491394,6.9772644-0.2585754,15.2600708-0.2585754c7.8978882,0,14.4650269-3.7466125,15.2462769,0.6028137
						c0,0,0.0163574,26.6996765,0.0163574,26.9947815c0,4.7679443-6.8362427,8.6295776-15.2626343,8.6295776
						C507.3746338,370.463501,500.5388184,366.6018677,500.5388184,361.8339233z"
                />
                <path
                  fill="#1F193B"
                  d="M500.5388184,357.2427368c0-0.0818787-0.0062866-27.265564,0-27.3436584
						c0.2275085-4.6529236,6.9772644-0.2623596,15.2600708-0.2623596c7.8978882,0,14.4650269-3.7428284,15.2462769,0.6028137
						c0,0,0.0163574,26.7038879,0.0163574,27.0032043c0,4.7595215-6.8362427,8.6211548-15.2626343,8.6211548
						C507.3746338,365.8638916,500.5388184,362.0022583,500.5388184,357.2427368z"
                />
                <path
                  fill="#138391"
                  d="M500.5388184,329.8990784c0-4.7675171,6.8358154-8.6291504,15.2600708-8.6291504
						c8.4263916,0,15.2626343,3.8616333,15.2626343,8.6291504c0,4.7637329-6.8362427,8.6253662-15.2626343,8.6253662
						C507.3746338,338.5244446,500.5388184,334.6628113,500.5388184,329.8990784z"
                />
              </g>
            </g>
          </g>
          <g>
            <g>
              <g>
                <path
                  fill="#566472"
                  d="M471.6558228,376.2808838c0-0.0864563,0-27.2617798,0-27.3436279
						c0.2338257-4.6487427,6.973053-0.2623901,15.2621765-0.2623901c7.9020691,0,14.4692078-3.7427979,15.2521362,0.6065979
						c0,0,0.0104675,26.6959229,0.0104675,26.9994202c0,4.7591248-6.8320313,8.6211548-15.2626038,8.6211548
						S471.6558228,381.0400085,471.6558228,376.2808838z"
                />
                <path
                  fill="#138391"
                  d="M471.6558228,371.6812744c0-0.0822754,0-27.2617798,0-27.3436279
						c0.2338257-4.6529236,6.973053-0.2623596,15.2621765-0.2623596c7.9020691,0,14.4692078-3.7428284,15.2521362,0.6065674
						c0,0,0.0104675,26.7001343,0.0104675,26.9994202c0,4.7591248-6.8320313,8.6211853-15.2626038,8.6211853
						S471.6558228,376.4403992,471.6558228,371.6812744z"
                />
                <path
                  fill="#B6D9D2"
                  d="M471.6558228,344.3376465c0-4.759552,6.831604-8.6253662,15.2621765-8.6253662
						s15.2626038,3.8658142,15.2626038,8.6253662c0,4.7633057-6.8320313,8.6253662-15.2626038,8.6253662
						S471.6558228,349.1009521,471.6558228,344.3376465z"
                />
              </g>
            </g>
          </g>
        </g>
        <g id="house-node">
          <g>
            <g>
              <polygon
                fill="#1C1C1C"
                points="1544.331665,298.042572 1437.2918701,236.5729523 1544.3408203,175.097229
					1651.3775635,236.5729523 				"
              />
            </g>
            <g>
              <g>
                <polygon
                  fill="#DBDBDB"
                  points="1543.1791992,323.4718933 1391.8942871,236.6083374 1391.8942871,219.3300781
						1694.4694824,219.3300781 1694.4694824,236.6083374 					"
                />
                <polygon
                  fill="#AAB7B7"
                  points="1543.1791992,323.4718933 1543.1791992,306.2168274 1694.4694824,219.3300781
						1694.4694824,236.6083374 					"
                />
                <polygon
                  fill="#DBE56B"
                  points="1543.1791992,306.2168274 1391.8942871,219.3300781 1543.1816406,132.4524994
						1694.4694824,219.3300781 					"
                />
              </g>
              <g>
                <g>
                  <path
                    fill="#D35400"
                    d="M1452.6934814,150.2164154c-1.6741943,0-3.0268555,0.6790466-3.0268555,1.5252686
							c0,0.6491547,0,45.6269379,0,46.2767029c0,0.2202454,0.0921631,0.4344025,0.2655029,0.6192627v-0.1073761
							c0.2470703,0.2745514,0.6582031,0.5063934,1.1646729,0.66745v0.1073914
							c0.461792,0.1488647,1.0126953,0.2385406,1.5966797,0.2385406c1.677124,0,3.0358887-0.6912537,3.0358887-1.5252686
							c0-0.649765,0-45.6275482,0-46.2767029C1455.7293701,150.895462,1454.3706055,150.2164154,1452.6934814,150.2164154z"
                  />
                </g>
                <g>
                  <path
                    fill="#7BA525"
                    d="M1452.362793,124.6382294c-7.34021,0-19.6137695,36.2965546-19.6137695,44.1437683
							c0,5.4397278,4.8167725,16.3728638,19.6137695,16.3728638c14.0220947,0,19.6082764-10.933136,19.6082764-16.3728638
							C1471.9710693,160.9347839,1457.78479,124.6382294,1452.362793,124.6382294z"
                  />
                  <path
                    fill="#9BBE23"
                    d="M1453.0625,169.4433594c1.6748047-15.3186035-0.7174072-44.7990265-0.7174072-44.7990265
							c-7.3408203,0.0384369-19.7901611,36.2965546-19.5960693,44.1376648
							C1432.9539795,177.1764832,1451.9814453,179.3454132,1453.0625,169.4433594z"
                  />
                  <path
                    fill="#C3DE59"
                    d="M1442.1507568,167.5129852c-3.6734619-0.1195831-7.1383057-1.9785919-7.1230469-3.8314819
							c0.0384521-6.2798462,8.0821533-26.8411102,13.8226318-35.7663803
							c3.2860107-3.800972,0.4916992,26.4481888-0.932251,36.5113144
							C1447.4978027,167.440979,1443.3990479,167.5483704,1442.1507568,167.5129852L1442.1507568,167.5129852z"
                  />
                  <g>
                    <path
                      fill="#698C15"
                      d="M1453.8404541,178.5711823c-12.6787109,1.5545654-18.6815186-2.0377502-18.6815186-2.0377502
								c2.7467041,4.462326,8.0759277,8.6214294,17.2038574,8.6214294c13.2685547,0,18.9768066-9.7836914,19.5515137-15.437561
								C1470.4993896,172.8453217,1462.6552734,177.4864197,1453.8404541,178.5711823z"
                    />
                  </g>
                  <path
                    fill="#D8EF73"
                    d="M1447.2536621,140.6474762c-1.7753906,5.1779785-4.036377,9.0924377-5.0406494,8.7465057
							c-1.0036621-0.3453217-0.3843994-4.8198547,1.3885498-9.9972382c1.7784424-5.1749268,4.0395508-9.089386,5.0462646-8.7440643
							C1449.6519775,130.9986115,1449.0291748,135.4731445,1447.2536621,140.6474762z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <polygon
                      fill="#FFFFFF"
                      points="1629.5565186,167.7985077 1627.7779541,166.3928223 1603.814209,180.1257477
								1603.814209,215.3143463 1605.5957031,216.291748 							"
                    />
                    <polygon
                      fill="#DBDBDB"
                      points="1629.5565186,202.5697937 1629.5565186,167.3872986 1605.5957031,181.1147308
								1605.5957031,216.291748 							"
                    />
                    <polygon
                      fill="#566472"
                      points="1608.3631592,182.0323334 1626.7890625,171.4805145 1626.7890625,201.6528015
								1608.3631592,212.20401 							"
                    />
                  </g>
                  <polygon
                    fill="#DBDBDB"
                    points="1617.4372559,175.8482819 1617.4372559,207.8490601 1618.8289795,207.0565338
							1618.8289795,175.0557404 						"
                  />
                </g>
                <g>
                  <g>
                    <polygon
                      fill="#C1382A"
                      points="1547.7336426,149.3939819 1648.2885742,207.0089417 1547.7336426,264.6116943
								1447.170166,207.0089417 							"
                    />
                    <g>
                      <polygon
                        fill="#DBDBDB"
                        points="1447.170166,216.7682343 1547.7336426,274.3832092 1547.7336426,264.6116943
									1447.170166,207.0089417 								"
                      />
                      <polygon
                        fill="#AAB7B7"
                        points="1547.7336426,274.3832092 1648.2885742,216.7682343 1648.2885742,207.0089417
									1547.7336426,264.6116943 								"
                      />
                    </g>
                  </g>
                  <path
                    fill="#DBDBDB"
                    d="M1474.1661377,178.7914429v21.0078583l1.5704346,0.9060059v-22.7301941
							C1475.2058105,178.2252655,1474.6787109,178.4936981,1474.1661377,178.7914429z"
                  />
                  <path
                    fill="#DBDBDB"
                    d="M1464.5771484,183.84375v21.02005l1.5697021,0.8938141V183.02742
							C1465.6166992,183.2775574,1465.0889893,183.5521088,1464.5771484,183.84375z"
                  />
                  <path
                    fill="#DBDBDB"
                    d="M1469.2834473,181.4069672v21.0200653l1.5704346,0.8938141v-22.7301941
							C1470.3261719,180.8407898,1469.7960205,181.1147308,1469.2834473,181.4069672z"
                  />
                  <g>
                    <polygon
                      fill="#DBDBDB"
                      points="1458.6395264,203.7137451 1460.8438721,204.9888763 1515.9799805,173.0057831
								1513.772583,171.7312622 							"
                    />
                    <polygon
                      fill="#AAB7B7"
                      points="1460.7962646,209.5531006 1516.0068359,177.5340118 1515.9799805,173.0057831
								1460.8438721,204.9888763 							"
                    />
                  </g>
                  <g>
                    <polygon
                      fill="#DBDBDB"
                      points="1458.2880859,185.5776672 1460.4924316,186.8643951 1515.62854,154.8758087
								1513.4241943,153.6006775 							"
                    />
                    <polygon
                      fill="#AAB7B7"
                      points="1460.447876,191.4286041 1515.6523438,159.4040222 1515.62854,154.8758087
								1460.4924316,186.8643951 							"
                    />
                  </g>
                  <g>
                    <polygon
                      fill="#AAB7B7"
                      points="1462.3129883,144.9255524 1460.1025391,146.2006836 1460.1025391,209.9405212
								1462.3129883,208.6708832 							"
                    />
                    <polygon
                      fill="#DBDBDB"
                      points="1456.1697998,143.8651886 1456.1697998,207.6879883 1460.1025391,209.9405212
								1460.1025391,146.2006836 							"
                    />
                  </g>
                  <g>
                    <path
                      fill="#AAB7B7"
                      d="M1466.1468506,184.9224091v20.8352051l0.8817139-0.5063934v-20.621048
								C1466.7369385,184.7198639,1466.4385986,184.815033,1466.1468506,184.9224091z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#AAB7B7"
                      d="M1470.8538818,182.479538v20.8413086l0.8846436-0.5063934v-20.6271515
								C1471.4462891,182.2830811,1471.1485596,182.3782654,1470.8538818,182.479538z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#AAB7B7"
                      d="M1475.7365723,179.8640137v20.8412933l0.8846436-0.5063934v-20.6271515
								C1476.322876,179.6669464,1476.0281982,179.7627258,1475.7365723,179.8640137z"
                    />
                  </g>
                  <polygon
                    fill="#AAB7B7"
                    points="1547.7336426,259.4757996 1547.7336426,195.7420807 1639.3304443,143.269104
							1639.3304443,207.0089417 						"
                  />
                  <polygon
                    fill="#DBDBDB"
                    points="1501.9249268,103.230751 1456.1313477,143.269104 1456.1313477,159.5583801
							1478.6706543,172.4756012 1478.6706543,219.9261627 1547.7336426,259.4757996 1547.7336426,195.7420807 						"
                  />
                  <polygon
                    fill="#C1382A"
                    points="1503.2683105,96.5043106 1551.9104004,194.7707825 1643.5133057,142.2923279
							1594.8712158,44.0221901 						"
                  />
                  <polygon
                    fill="#87201B"
                    points="1451.9429932,141.3625183 1451.9429932,143.9243622 1456.1313477,146.3196411
							1456.1313477,143.269104 1501.9249268,103.230751 1547.7336426,195.7420807 1547.7336426,198.7987213
							1551.9104004,201.1940002 1551.9104004,194.7707825 1503.2683105,96.5043106 						"
                  />
                  <polygon
                    fill="#991A17"
                    points="1551.9104004,201.1940002 1551.9104004,194.7707825 1643.5133057,142.2923279
							1643.5133057,148.7149353 						"
                  />
                </g>
                <g>
                  <g>
                    <polygon
                      fill="#FFFFFF"
                      points="1589.1873779,191.3987122 1587.4119873,189.9985199 1563.4421387,203.7320557
								1563.4421387,238.9084473 1565.2236328,239.8974304 							"
                    />
                    <polygon
                      fill="#DBDBDB"
                      points="1589.1873779,226.1699982 1589.1873779,190.9813995 1565.2236328,204.7149353
								1565.2236328,239.8974304 							"
                    />
                    <polygon
                      fill="#566472"
                      points="1567.9971924,205.6386414 1586.4168701,195.080719 1586.4168701,225.2462921
								1567.9971924,235.810318 							"
                    />
                  </g>
                  <polygon
                    fill="#DBDBDB"
                    points="1577.0712891,199.4423828 1577.0712891,231.443161 1578.4537354,230.6506348
							1578.4537354,198.6553345 						"
                  />
                </g>
                <g>
                  <g>
                    <polygon
                      fill="#FFFFFF"
                      points="1631.0787354,167.3872986 1629.3001709,165.9816132 1605.3394775,179.7151489
								1605.3394775,214.897644 1607.1179199,215.8921204 							"
                    />
                    <polygon
                      fill="#DBDBDB"
                      points="1631.0787354,202.1530914 1631.0787354,166.9766998 1607.1179199,180.7041321
								1607.1179199,215.8921204 							"
                    />
                    <polygon
                      fill="#566472"
                      points="1609.885376,181.6217346 1628.3082275,171.0638123 1628.3082275,201.2415924
								1609.885376,211.7934113 							"
                    />
                  </g>
                  <polygon
                    fill="#DBDBDB"
                    points="1618.9655762,175.4309692 1618.9655762,207.4317474 1620.3481445,206.6392212
							1620.3481445,174.6384277 						"
                  />
                </g>
                <g>
                  <g>
                    <polygon
                      fill="#FFFFFF"
                      points="1504.2786865,191.3987122 1506.0571289,189.9985199 1530.0203857,203.7320557
								1530.0203857,238.9084473 1528.2388916,239.8974304 							"
                    />
                    <polygon
                      fill="#EFEFEF"
                      points="1504.2786865,226.1699982 1504.2786865,190.9813995 1528.2388916,204.7149353
								1528.2388916,239.8974304 							"
                    />
                    <polygon
                      fill="#566472"
                      points="1525.465332,205.6386414 1507.0461426,195.080719 1507.0461426,225.2462921
								1525.465332,235.810318 							"
                    />
                  </g>
                  <polygon
                    fill="#EFEFEF"
                    points="1516.394165,199.4423828 1516.394165,231.443161 1515.0062256,230.6506348
							1515.0062256,198.6553345 						"
                  />
                </g>
                <g>
                  <g>
                    <polygon
                      fill="#FFFFFF"
                      points="1486.8808594,126.899292 1488.65625,125.496048 1502.6459961,133.3548431
								1502.6459961,167.953476 1500.8675537,168.9369659 							"
                    />
                    <polygon
                      fill="#EFEFEF"
                      points="1486.8808594,161.5009766 1486.8808594,126.9023438 1500.8675537,134.7611542
								1500.8675537,169.3536682 							"
                    />
                    <polygon
                      fill="#566472"
                      points="1498.0939941,135.2614441 1489.6483154,130.5782471 1489.6483154,160.1605682
								1498.0939941,164.84375 							"
                    />
                  </g>
                </g>
                <g>
                  <polygon
                    fill="#EFF3F3"
                    points="1442.2844238,219.86026 1451.0101318,215.3564453 1478.5125732,230.5670471
							1469.7844238,235.506485 						"
                  />
                  <polygon
                    fill="#AAB7B7"
                    points="1469.7844238,239.569809 1478.5125732,234.6248779 1478.5125732,230.5670471
							1469.7844238,235.506485 						"
                  />
                  <polygon
                    fill="#BDC3C7"
                    points="1469.7844238,239.569809 1442.2844238,223.923584 1442.2844238,219.86026
							1469.7844238,235.506485 						"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      fill="#61913D"
                      d="M1587.90625,264.0754089c5.5976563,0,9.94104-2.8180847,9.94104-4.8082581
								c0-2.6869202,0-6.4049072,0-6.4049072c0-2.7583008-2.4398193-8.2938232-9.94104-8.2938232
								c-7.1108398,0-9.9411621,5.5355225-9.9411621,8.2938232c0,0,0,4.4867249,0,6.4945984
								C1577.9650879,261.2811279,1582.0998535,264.0754089,1587.90625,264.0754089z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#9BC04E"
                      d="M1596.2060547,249.9190826c-0.2208252,2.6454163-3.890625,4.5520172-8.2016602,4.2420807
								c-4.3134766-0.3099365-7.6348877-2.6930237-7.411499-5.3384552c0.2232666-2.6454315,3.8991699-4.5459137,8.2102051-4.2542877
								C1593.1165771,244.8783569,1596.4294434,247.2614441,1596.2060547,249.9190826z"
                    />
                    <path
                      fill="#BAD950"
                      d="M1594.314209,247.7501373c-0.2172852,1.1140594-2.3892822,1.6265564-4.8527832,1.1500549
								c-2.4605713-0.4764862-4.2811279-1.7693176-4.0633545-2.8894653c0.2171631-1.1262665,2.3891602-1.6387482,4.8527832-1.162262
								C1592.708374,245.3371582,1594.5313721,246.6360779,1594.314209,247.7501373z"
                    />
                    <path
                      fill="#E4F991"
                      d="M1592.1812744,246.284668c-0.0836182,0.4227905-0.9053955,0.6192474-1.8382568,0.4411011
								c-0.932251-0.1787567-1.6235352-0.6735535-1.539917-1.1085663c0.0804443-0.4167023,0.9084473-0.607666,1.8352051-0.4289093
								C1591.5736084,245.3609619,1592.2617188,245.8673401,1592.1812744,246.284668z"
                    />
                  </g>
                </g>
                <g>
                  <g>
                    <path
                      fill="#698C15"
                      d="M1570.6309814,275.4850464c7.4354248,0,13.1972656-3.7533875,13.1972656-6.3927002
								c0-3.5807495,0-8.4963684,0-8.4963684c0-3.646637-3.2414551-11.0045319-13.1972656-11.0045319
								c-9.4316406,0-13.1911621,7.3578949-13.1911621,11.0045319c0,0,0,5.958313,0,8.6092224
								C1557.4398193,271.7731628,1562.927124,275.4850464,1570.6309814,275.4850464z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#7BA525"
                      d="M1581.6531982,256.6815186c-0.2977295,3.5032349-5.1743164,6.0351868-10.8970947,5.6422729
								c-5.7198486-0.4051208-10.119873-3.5691223-9.8282471-7.0900574
								c0.2922363-3.5215454,5.171875-6.0297089,10.8946533-5.6422882
								C1577.5452881,249.9965668,1581.9454346,253.1721802,1581.6531982,256.6815186z"
                    />
                    <path
                      fill="#C3DE59"
                      d="M1579.1358643,253.8097382c-0.2861328,1.4776764-3.1663818,2.1689301-6.4348145,1.5313721
								c-3.2677002-0.6375732-5.6806641-2.3538055-5.397583-3.8430786c0.2855225-1.4837799,3.1695557-2.1750336,6.4403076-1.5374756
								C1577.0151367,250.5981293,1579.4281006,252.3259583,1579.1358643,253.8097382z"
                    />
                    <path
                      fill="#D8EF73"
                      d="M1576.3087158,251.8732452c-0.112915,0.5478821-1.2062988,0.8102264-2.4429932,0.5722961
								c-1.2359619-0.2385559-2.1535645-0.8938141-2.0432129-1.4599915c0.1098633-0.5539856,1.203125-0.8102264,2.4398193-0.5838776
								C1575.5014648,250.6518097,1576.4160156,251.3070679,1576.3087158,251.8732452z"
                    />
                  </g>
                </g>
                <g>
                  <g>
                    <path
                      fill="#61913D"
                      d="M1555.458252,280.5495605c5.0290527,0,8.9251709-2.5319519,8.9251709-4.3079834
								c0-2.4367676,0-5.749054,0-5.749054c0-2.4666443-2.1950684-7.4420776-8.9251709-7.4420776
								c-6.3719482,0-8.9191895,4.9754333-8.9191895,7.4420776c0,0,0,4.0212402,0,5.8210449
								C1546.5390625,278.0414124,1550.2540283,280.5495605,1555.458252,280.5495605z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#9BC04E"
                      d="M1562.9088135,267.8471069c-0.2025146,2.3708801-3.4970703,4.0871277-7.3609619,3.8009644
								c-3.869873-0.2623291-6.8459473-2.4129639-6.6434326-4.7722473c0.1940918-2.3830872,3.494751-4.0871277,7.3585205-3.8253784
								C1560.1328125,263.3365784,1563.105957,265.4695129,1562.9088135,267.8471069z"
                    />
                    <path
                      fill="#BAD950"
                      d="M1561.2078857,265.898407c-0.1934814,1.0072937-2.1414795,1.4715881-4.3464355,1.0372009
								c-2.213501-0.4349976-3.8430176-1.5850525-3.6490479-2.5923462c0.1933594-1.0066833,2.1390381-1.4770813,4.3525391-1.0426941
								C1559.7692871,263.7416992,1561.4073486,264.8917542,1561.2078857,265.898407z"
                    />
                    <path
                      fill="#E4F991"
                      d="M1559.2982178,264.5939941c-0.0775146,0.3691406-0.8156738,0.5478821-1.6502686,0.3868103
								c-0.84021-0.1604309-1.4564209-0.6015625-1.3850098-0.9889832c0.0744629-0.3752136,0.8132324-0.5478516,1.6534424-0.3929138
								C1558.7473145,263.7654724,1559.3726807,264.212677,1559.2982178,264.5939941z"
                    />
                  </g>
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      fill="#61913D"
                      d="M1647.3112793,230.2034302c5.6037598,0,9.9440918-2.8296814,9.9440918-4.8137512
								c0-2.6991272,0-6.4049225,0-6.4049225c0-2.7528076-2.4428711-8.2999115-9.9440918-8.2999115
								c-7.1047363,0-9.9411621,5.5471039-9.9411621,8.2999115c0,0,0,4.4806366,0,6.493988
								C1637.3701172,227.3975372,1641.5053711,230.2034302,1647.3112793,230.2034302z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#9BC04E"
                      d="M1655.6141357,216.0354919c-0.2208252,2.6454315-3.8967285,4.5459137-8.2047119,4.2481842
								c-4.3104248-0.303833-7.6293945-2.6930084-7.4085693-5.3384399c0.2233887-2.651535,3.8968506-4.5520172,8.2103271-4.2481842
								C1652.5185547,211.000885,1655.8344727,213.3900604,1655.6141357,216.0354919z"
                    />
                    <path
                      fill="#BAD950"
                      d="M1653.7253418,213.8726654c-0.2203369,1.1140594-2.392334,1.6326447-4.8529053,1.1500549
								c-2.4605713-0.482605-4.2841797-1.7760315-4.0662842-2.8955841c0.2202148-1.1201477,2.3861084-1.6326447,4.8466797-1.1561432
								C1652.1195068,211.4535828,1653.9370117,212.7525024,1653.7253418,213.8726654z"
                    />
                    <path
                      fill="#E4F991"
                      d="M1651.5893555,212.4071808c-0.0836182,0.4167023-0.9053955,0.6192627-1.8413086,0.4344025
								c-0.932251-0.1842499-1.6235352-0.6668549-1.5368652-1.0957642c0.0834961-0.4234161,0.9023438-0.6198578,1.8382568-0.4349976
								C1650.9816895,211.4895782,1651.6728516,211.9837646,1651.5893555,212.4071808z"
                    />
                  </g>
                </g>
                <g>
                  <g>
                    <path
                      fill="#7BA525"
                      d="M1630.0421143,241.613678c7.4354248,0,13.1942139-3.771698,13.1942139-6.3994293
								c0-3.5868225,0-8.4957581,0-8.4957581c0-3.6643066-3.2385254-11.0106201-13.1942139-11.0106201
								c-9.4407959,0-13.1942139,7.3463135-13.1942139,11.0106201c0,0,0,5.9515991,0,8.6031342
								C1616.8479004,237.8895721,1622.3352051,241.613678,1630.0421143,241.613678z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#9BBE23"
                      d="M1641.0582275,222.8040314c-0.291626,3.5087433-5.1682129,6.035202-10.8909912,5.6361847
								c-5.7229004-0.3935242-10.1229248-3.5691376-9.8282471-7.0900726
								c0.2922363-3.5154419,5.171875-6.0357971,10.8885498-5.6422729
								C1636.9503174,216.1190796,1641.355957,219.2885895,1641.0582275,222.8040314z"
                    />
                    <path
                      fill="#C3DE59"
                      d="M1638.5469971,219.9261627c-0.2861328,1.4831696-3.1694336,2.1744232-6.4378662,1.5313568
								c-3.2646484-0.637558-5.6867676-2.3598938-5.397583-3.8430634c0.291626-1.4837799,3.1663818-2.1689301,6.4342041-1.5313721
								C1636.4171143,216.7206421,1638.8361816,218.436264,1638.5469971,219.9261627z"
                    />
                    <path
                      fill="#D8EF73"
                      d="M1635.7111816,217.9896698c-0.104248,0.5600739-1.1976318,0.81633-2.4373779,0.577774
								c-1.2421875-0.2501373-2.1506348-0.899292-2.0462646-1.4538879c0.1104736-0.565567,1.2037354-0.8218079,2.4428711-0.5893707
								C1634.9064941,216.7743378,1635.8240967,217.4234924,1635.7111816,217.9896698z"
                    />
                  </g>
                </g>
                <g>
                  <g>
                    <path
                      fill="#61913D"
                      d="M1614.8693848,246.6720734c5.0229492,0,8.916626-2.5386505,8.916626-4.3195648
								c0-2.4190674,0-5.7441711,0-5.7441711c0-2.4721527-2.1865234-7.4414825-8.916626-7.4414825
								c-6.3781738,0-8.9161377,4.9693298-8.9161377,7.4414825c0,0,0,4.0218353,0,5.8155518
								C1605.9532471,244.1578217,1609.6590576,246.6720734,1614.8693848,246.6720734z"
                    />
                  </g>
                  <g>
                    <path
                      fill="#9BC04E"
                      d="M1622.3168945,233.9690094c-0.1994629,2.3714905-3.4940186,4.0694275-7.3640137,3.8076935
								c-3.869873-0.2684479-6.8398438-2.407486-6.6434326-4.7905731c0.2000732-2.3769836,3.494751-4.0755157,7.364624-3.8192749
								C1619.5408936,229.4407959,1622.5170898,231.579834,1622.3168945,233.9690094z"
                    />
                    <path
                      fill="#BAD950"
                      d="M1620.612915,232.0270386c-0.1873779,1.0066833-2.1384277,1.4654846-4.3464355,1.0304718
								c-2.2073975-0.4289093-3.8424072-1.5966492-3.6490479-2.5917358
								c0.1994629-1.0066833,2.1451416-1.4776764,4.3525391-1.0426788
								C1619.1772461,229.8465118,1620.8099365,231.014267,1620.612915,232.0270386z"
                    />
                    <path
                      fill="#E4F991"
                      d="M1618.7038574,230.7043304c-0.0744629,0.3813171-0.810791,0.5600739-1.6503906,0.3990021
								c-0.8345947-0.1787567-1.4538574-0.6015625-1.3793945-0.9889832
								c0.0714111-0.3697205,0.8101807-0.5478668,1.6442871-0.3874207
								C1618.1584473,229.881897,1618.7783203,230.3291168,1618.7038574,230.7043304z"
                    />
                  </g>
                </g>
              </g>
              <g>
                <g>
                  <path
                    fill="#D35400"
                    d="M1663.9403076,182.1635132c-1.6801758,0-3.0328369,0.6851501-3.0328369,1.5252686
							c0,0.6436615,0,45.6269379,0,46.2767029c0,0.2202454,0.0958252,0.4289093,0.2653809,0.6253662v-0.1128693
							c0.2470703,0.273941,0.6552734,0.5002899,1.1616211,0.6613464v0.1128693
							c0.4710693,0.1549683,1.0189209,0.2263641,1.605835,0.2263641c1.6711426,0,3.0328369-0.6729584,3.0328369-1.5130768
							c0-0.649765,0-45.6330414,0-46.2767029C1666.9731445,182.8486633,1665.6114502,182.1635132,1663.9403076,182.1635132z"
                  />
                </g>
                <g>
                  <path
                    fill="#7BA525"
                    d="M1663.6004639,156.5853271c-7.3371582,0-19.6107178,36.2971649-19.6107178,44.1437683
							c0,5.4336243,4.8168945,16.3667603,19.6107178,16.3667603c14.0288086,0,19.6082764-10.933136,19.6082764-16.3667603
							C1683.2087402,192.8824921,1669.022583,156.5853271,1663.6004639,156.5853271z"
                  />
                  <path
                    fill="#9BBE23"
                    d="M1664.3039551,201.3904572c1.6802979-15.3186035-0.7181396-44.7990265-0.7181396-44.7990265
							c-7.34021,0.0359955-19.7894287,36.2965546-19.5960693,44.1376648
							C1644.1893311,209.1241913,1663.2253418,211.292511,1664.3039551,201.3904572z"
                  />
                  <path
                    fill="#C3DE59"
                    d="M1653.3946533,199.4600677c-3.6759033-0.1189575-7.1376953-1.9724731-7.1260986-3.8308563
							c0.0390625-6.274353,8.0852051-26.8417206,13.8256836-35.7669983
							c3.2860107-3.8009644,0.4886475,26.4542999-0.9291992,36.5052185
							C1658.7391357,199.3886871,1654.6398926,199.5015564,1653.3946533,199.4600677L1653.3946533,199.4600677z"
                  />
                  <g>
                    <path
                      fill="#698C15"
                      d="M1665.078125,210.5121918c-12.6755371,1.561264-18.6723633-2.0255585-18.6723633-2.0255585
								c2.7407227,4.4562225,8.0675049,8.6092224,17.1947021,8.6092224c13.2717285,0,18.9798584-9.7769775,19.5545654-15.437561
								C1681.7402344,204.7924194,1673.8961182,209.4335175,1665.078125,210.5121918z"
                    />
                  </g>
                  <path
                    fill="#D8EF73"
                    d="M1658.4975586,172.5951843c-1.7784424,5.1773682-4.0334473,9.0918274-5.0432129,8.7465057
							c-1.0072021-0.345932-0.3842773-4.8204651,1.394165-9.9978485c1.7723389-5.171875,4.0334473-9.0924377,5.0400391-8.7465057
							C1660.8928223,162.9426575,1660.2698975,167.4232941,1658.4975586,172.5951843z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <path
                    fill="#D35400"
                    d="M1542.0108643,248.9178925c-1.6772461,0-3.0354004,0.6912537-3.0354004,1.5313721
							c0,0.6436615,0,45.6269379,0,46.2645111c0,0.2324524,0.0982666,0.4411011,0.2648926,0.6375427v-0.1134644
							c0.2476807,0.2745361,0.661377,0.500885,1.168335,0.6613464v0.1134949
							c0.4642334,0.1427612,1.0128174,0.2324524,1.6021729,0.2324524c1.6716309,0,3.0328369-0.6790466,3.0328369-1.5313721
							c0-0.6375732,0-45.6208496,0-46.2645111C1545.0437012,249.6091461,1543.6824951,248.9178925,1542.0108643,248.9178925z"
                  />
                </g>
                <g>
                  <path
                    fill="#7BA525"
                    d="M1541.6685791,202.736969c-7.34021,0-19.6082764,56.8999023-19.6082764,64.7526245
							c0,5.4336243,4.8173828,16.3728638,19.6082764,16.3728638c14.0281982,0,19.6137695-10.9392395,19.6137695-16.3728638
							C1561.2823486,259.6368713,1547.0960693,202.736969,1541.6685791,202.736969z"
                  />
                  <path
                    fill="#9BBE23"
                    d="M1542.37146,268.1448364c1.6801758-15.3179779-0.7150879-65.2230072-0.7150879-65.2230072
							c-7.34021,0.0414886-19.786377,56.7266388-19.5960693,64.5677643
							C1522.2597656,275.8840637,1541.293335,278.053009,1542.37146,268.1448364z"
                  />
                  <path
                    fill="#C3DE59"
                    d="M1531.4620361,266.2205505c-3.6697998-0.1073608-7.1376953-1.9840698-7.1224365-3.8253784
							c0.0384521-6.2859497,8.6208496-48.0875092,14.3620605-57.0127716
							c3.282959-3.80159-0.0476074,47.6945953-1.4692383,57.7516327
							C1536.8096924,266.1485596,1532.7133789,266.2559509,1531.4620361,266.2205505L1531.4620361,266.2205505z"
                  />
                  <g>
                    <path
                      fill="#698C15"
                      d="M1543.1462402,277.2726746c-12.6732178,1.5606384-18.6699219-2.0316772-18.6699219-2.0316772
								c2.7406006,4.4562378,8.0644531,8.62146,17.1922607,8.62146c13.27771,0,18.9822998-9.7830811,19.5576172-15.431488
								C1559.8106689,271.5589905,1551.9702148,276.1940002,1543.1462402,277.2726746z"
                    />
                  </g>
                  <path
                    fill="#D8EF73"
                    d="M1538.0042725,223.4653931c-1.6864014,8.0967407-4.1975098,14.4302826-5.6124268,14.150238
							c-1.4208984-0.303833-1.2006836-7.1138611,0.4825439-15.2173157c1.6833496-8.1144409,4.1945801-14.4479675,5.609375-14.150238
							C1539.9017334,208.5403137,1539.6844482,215.3564453,1538.0042725,223.4653931z"
                  />
                </g>
              </g>
            </g>
            <g>
              <g>
                <g>
                  <g>
                    <polygon
                      fill="#DBDBDB"
                      points="1373.7276611,218.8950653 1378.4017334,216.1605682 1378.4017334,63.4248543
								1373.7276611,66.1477661 							"
                    />
                    <polygon
                      fill="#AAB7B7"
                      points="1373.7276611,218.8950653 1369.5753174,216.5241852 1369.5753174,63.7854271
								1373.7276611,66.1477661 							"
                    />
                  </g>
                </g>
              </g>
              <polygon
                fill="#F7F7F7"
                points="1369.5753174,63.7854271 1374.2554932,61.0625153 1378.4017334,63.4248543
					1373.7276611,66.1477661 				"
              />
            </g>
            <g>
              <path
                fill="#15D195"
                d="M1478.6798096,130.1853333l0.3276367-3.7478943
					c-0.5302734-0.0414886-53.5693359-5.308548-103.8764648-61.541008l-2.8065186,2.5026703
					C1423.6864014,124.8108902,1478.1373291,130.1407928,1478.6798096,130.1853333z"
              />
            </g>
          </g>
          <g>
            <path
              fill="#AAB7B7"
              d="M1475.6469727,128.7997742c0,1.7131958,1.385498,3.0956879,3.0949707,3.0956879
				c1.7132568,0,3.0957031-1.3824921,3.0957031-3.0956879c0-1.7064667-1.3824463-3.0950699-3.0957031-3.0950699
				C1477.0324707,125.7047043,1475.6469727,127.0933075,1475.6469727,128.7997742z"
            />
            <path
              fill="#DBDBDB"
              d="M1478.7419434,125.7047043c0.2655029,0,0.5095215,0.0896835,0.7541504,0.1549683
				c0.3306885,0.491745,0.574707,1.0518265,0.574707,1.6918259c0,1.7101288-1.3848877,3.0926361-3.1011963,3.0926361
				c-0.2653809,0-0.5002441-0.0896759-0.7479248-0.1488647c-0.3306885-0.4978485-0.574707-1.0548706-0.574707-1.6954956
				C1475.6469727,127.0933075,1477.0324707,125.7047043,1478.7419434,125.7047043z"
            />
          </g>
        </g>
        <g id="battery-node">
          <g>
            <g>

              <linearGradient
                id="SVGID_1_"
                gradientUnits="userSpaceOnUse"
                x1="1331.354126"
                y1="338.484314"
                x2="1396.4107666"
                y2="338.484314"
              >
                <stop
                  offset="0"
                  style="stop-color:#DBDBDB"
                />
                <stop
                  offset="1"
                  style="stop-color:#EDEDED"
                />
              </linearGradient>
              <path
                fill="url(#SVGID_1_)"
                d="M1386.5710449,298.4648438v-0.0001831
					c-12.8605957,7.2166443-33.4197998,7.0089722-45.9204102-0.4638672
					c-6.147583-3.6748962-9.2011719-8.4224854-9.1878662-13.1573181l-0.1086426,88.3809814
					c-0.0133057,4.7348328,3.0404053,9.4824219,9.1878662,13.1573181
					c12.5006104,7.4728394,33.0598145,7.6805115,45.9204102,0.4640808
					c6.5361328-3.6677246,9.8256836-8.5471191,9.8397217-13.4400635l0.1086426-88.3811951
					C1396.3967285,289.9177551,1393.1071777,294.7971497,1386.5710449,298.4648438z"
              />
              <path
                fill="#F4F4F4"
                d="M1387.2227783,271.8674622c-12.5004883-7.4728088-33.0598145-7.6806641-45.9204102-0.4640503
					c-12.8605957,7.2164307-13.1524658,19.1245728-0.6517334,26.5973816
					c12.5006104,7.4728394,33.0598145,7.6805115,45.9204102,0.4638672
					C1399.4316406,291.24823,1399.7233887,279.3403015,1387.2227783,271.8674622z M1378.529541,293.6575623
					c-8.2915039,4.6527405-21.5465088,4.5187683-29.605957-0.2991333
					c-8.0593262-4.8178711-7.8712158-12.4951782,0.4202881-17.1479187
					c8.291626-4.6525269,21.5465088-4.5187378,29.6060791,0.2991333
					C1387.0091553,281.3275146,1386.8210449,289.0050049,1378.529541,293.6575623z"
              />

              <linearGradient
                id="SVGID_2_"
                gradientUnits="userSpaceOnUse"
                x1="1343.0001221"
                y1="289.1637878"
                x2="1384.8835449"
                y2="289.1637878"
              >
                <stop
                  offset="0"
                  style="stop-color:#7C7C80"
                />
                <stop
                  offset="0.580488"
                  style="stop-color:#929296"
                />
                <stop
                  offset="1"
                  style="stop-color:#BDBDC5"
                />
              </linearGradient>
              <path
                fill="url(#SVGID_2_)"
                d="M1378.5397949,290.0481567c-8.2915039,4.6525269-21.5465088,4.5187378-29.605957-0.2991333
					c-3.963501-2.3694153-5.9321289-5.4302979-5.923584-8.4828186l-0.0101318,3.609436
					c-0.008667,3.0527039,1.960083,6.1134033,5.9234619,8.4827881c8.0594482,4.8179016,21.3144531,4.9518738,29.605957,0.2991333
					c4.2141113-2.364624,6.3348389-5.510376,6.3438721-8.6651306l0.0101318-3.6094055
					C1384.8746338,284.5377808,1382.7537842,287.6835327,1378.5397949,290.0481567z"
              />

              <linearGradient
                id="SVGID_3_"
                gradientUnits="userSpaceOnUse"
                x1="1343.0102539"
                y1="281.324585"
                x2="1384.883667"
                y2="281.324585"
              >
                <stop
                  offset="0"
                  style="stop-color:#C0C0C4"
                />
                <stop
                  offset="1"
                  style="stop-color:#D7D7DD"
                />
              </linearGradient>
              <path
                fill="url(#SVGID_3_)"
                d="M1378.960083,272.9002075c-8.0594482-4.8178711-21.3144531-4.9518433-29.605957-0.2991028
					c-8.2915039,4.6525269-8.4796143,12.3300476-0.4202881,17.1479187
					c8.0594482,4.8178711,21.3144531,4.9516602,29.605957,0.2991333
					C1386.8312988,285.3953857,1387.0195313,277.7181091,1378.960083,272.9002075z M1370.0841064,284.9932556
					c-3.4870605,1.9568481-9.0616455,1.9005432-12.4510498-0.125824c-3.3895264-2.0261536-3.3103027-5.2549744,0.1767578-7.2116394
					c3.4869385-1.956665,9.0616455-1.9003601,12.4510498,0.1257935
					C1373.6503906,279.8077393,1373.5712891,283.0365906,1370.0841064,284.9932556z"
              />
              <path
                fill="#9B9BA0"
                d="M1364.5498047,277.7576294c-1.557373-0.9310913-3.5760498-1.4460754-5.6416016-1.5411072
					c-0.1821289-0.008606-0.364624-0.0135803-0.5473633-0.0153809c-2.2531738-0.0227661-4.5186768,0.4522705-6.262085,1.4305115
					c-3.4870605,1.9568481-3.5662842,5.1856689-0.1767578,7.2118225c1.6881104,1.0091858,3.9180908,1.5297546,6.1624756,1.5563049
					c0.1004639,0.0012207,6.7216797,0.0014038,6.8222656,0.0006104L1364.5498047,277.7576294z"
              />

              <linearGradient
                id="SVGID_4_"
                gradientUnits="userSpaceOnUse"
                x1="1355.1418457"
                y1="282.0576782"
                x2="1372.7624512"
                y2="282.0576782"
              >
                <stop
                  offset="0"
                  style="stop-color:#7C7C80"
                />
                <stop
                  offset="0.580488"
                  style="stop-color:#929296"
                />
                <stop
                  offset="1"
                  style="stop-color:#BDBDC5"
                />
              </linearGradient>
              <path
                fill="url(#SVGID_4_)"
                d="M1370.0944824,281.3838196c-3.4871826,1.956665-9.0616455,1.9003601-12.4512939-0.1257935
					c-1.666626-0.9963989-2.494751-2.2837219-2.4912109-3.5674744l-0.0101318,3.6094055
					c-0.00354,1.2837524,0.8242188,2.5710754,2.4912109,3.5674744c3.3894043,2.0263672,8.9639893,2.0826721,12.4510498,0.125824
					c1.7723389-0.9944153,2.6643066-2.3172913,2.6679688-3.644165l0.010376-3.609436
					C1372.758667,279.0663452,1371.8666992,280.3894348,1370.0944824,281.3838196z"
              />

              <linearGradient
                id="SVGID_5_"
                gradientUnits="userSpaceOnUse"
                x1="1355.1520996"
                y1="277.7150269"
                x2="1372.7623291"
                y2="277.7150269"
              >
                <stop
                  offset="0"
                  style="stop-color:#D6D6DD"
                />
                <stop
                  offset="1"
                  style="stop-color:#DFDFE5"
                />
              </linearGradient>
              <path
                fill="url(#SVGID_5_)"
                d="M1370.2712402,274.1721802c-3.3896484-2.0263367-8.9641113-2.0826721-12.4512939-0.1257935
					c-3.4870605,1.956665-3.56604,5.1854858-0.1767578,7.2116394c3.3896484,2.0261536,8.9641113,2.0824585,12.4512939,0.1257935
					C1373.581543,279.4271545,1373.6605225,276.1983337,1370.2712402,274.1721802z"
              />
            </g>
            <path
              fill="#5B5B5B"
              d="M1366.9053955,311.941864v71.4724731c8.1300049-0.4141235,17.2659912-2.6972656,23.5626221-7.8504333
				v-71.4724731c-3.2593994,2.6675415-7.279541,4.5658875-11.5273438,5.834259
				C1374.982666,311.1073914,1370.8269043,311.7421875,1366.9053955,311.941864z"
            />
            <g id="battery-state">
              <path
                fill="#656568"
                d="M1385.8884277,316.0848389c-8.3552246,4.9680176-15.3360596,4.4472656-18.9830322,5.0624695v4.8462524
					c9.6218262-0.6395874,19.8344727-4.769165,23.5626221-8.2807312v-4.1070251
					C1389.1397705,313.4638367,1387.5625,315.1010132,1385.8884277,316.0848389z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,322.9770813c-8.3552246,4.9680481-15.3360596,4.4472656-18.9830322,5.0625v4.8462219
					c9.6218262-0.6395569,19.8344727-4.769165,23.5626221-8.2807312v-4.1070251
					C1389.1397705,320.3560791,1387.5625,321.9932556,1385.8884277,322.9770813z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,329.9028015c-8.3552246,4.9680481-15.3360596,4.4472656-18.9830322,5.0625v4.8462219
					c9.6218262-0.6395874,19.8344727-4.769165,23.5626221-8.2807312v-4.1070251
					C1389.1397705,327.2817993,1387.5625,328.9189758,1385.8884277,329.9028015z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,336.8390503c-8.3552246,4.9680481-15.3360596,4.4472961-18.9830322,5.0625v4.8462219
					c9.6218262-0.6395569,19.8344727-4.7691345,23.5626221-8.2807312v-4.1069946
					C1389.1397705,334.2180481,1387.5625,335.8552246,1385.8884277,336.8390503z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,343.6933594c-8.3552246,4.9680481-15.3360596,4.4472961-18.9830322,5.0625v4.8462219
					c9.6218262-0.6395569,19.8344727-4.7691345,23.5626221-8.2807312v-4.1069946
					C1389.1397705,341.0723572,1387.5625,342.7095337,1385.8884277,343.6933594z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,350.6220398c-8.3552246,4.9680176-15.3360596,4.4472656-18.9830322,5.0624695v4.8462524
					c9.6218262-0.6395874,19.8344727-4.769165,23.5626221-8.2807312v-4.1070251
					C1389.1397705,348.0010376,1387.5625,349.6382141,1385.8884277,350.6220398z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,357.5232544c-8.3552246,4.9680176-15.3360596,4.4472656-18.9830322,5.0624695v4.8462524
					c9.6218262-0.6395874,19.8344727-4.769165,23.5626221-8.2807312V355.04422
					C1389.1397705,354.9022522,1387.5625,356.5394287,1385.8884277,357.5232544z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,364.4431152c-8.3552246,4.9680481-15.3360596,4.4472656-18.9830322,5.0625v4.8462219
					c9.6218262-0.6395874,19.8344727-4.769165,23.5626221-8.2807312v-4.1070251
					C1389.1397705,361.822113,1387.5625,363.4592896,1385.8884277,364.4431152z"
              />
              <path
                fill="#656568"
                d="M1385.8884277,371.3381042c-8.3552246,4.9680481-15.3360596,4.4472656-18.9830322,5.0625v4.8462219
					c9.6218262-0.6395874,19.8344727-4.769165,23.5626221-8.2807312v-4.1070251
					C1389.1397705,368.7171021,1387.5625,370.3542786,1385.8884277,371.3381042z"
              />
              <path
                fill="#656568"
                d="M1366.9053955,319.0844116c7.8242188-0.5175781,16.75-3.1066284,23.5626221-8.3661804v-4.0259705
					c-0.4821777-0.0988159-1.0201416-0.0059814-1.5421143,0.3965759
					c-6.4702148,4.9532776-13.7752686,6.6064148-22.0205078,7.1800842V319.0844116z"
              />
              <radialGradient
                id="SVGID_6_"
                cx="1378.6867676"
                cy="312.8701477"
                r="9.4185133"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 99"
                fill="url(#SVGID_6_)"
                d="M1390.4680176,310.7182312v-4.0259705
					c-0.4821777-0.0988159-1.0201416-0.0059814-1.5421143,0.3967896c-9.4718018,7.2511597-17.876709,6.4422607-22.0205078,7.1417542
					v4.8536072C1374.7220459,318.5674438,1383.6671143,315.9666138,1390.4680176,310.7182312z"
              />
              <radialGradient
                id="SVGID_7_"
                cx="1378.6867676"
                cy="319.7953491"
                r="9.4185133"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 89"
                fill="url(#SVGID_7_)"
                d="M1390.4680176,317.6434326v-4.0259705
					c-0.4821777-0.0988159-1.0201416-0.0059814-1.5421143,0.3967896c-9.4718018,7.2511597-17.876709,6.4422607-22.0205078,7.1417542
					v4.8536072C1374.7220459,325.4926453,1383.6671143,322.8918152,1390.4680176,317.6434326z"
              />
              <radialGradient
                id="SVGID_8_"
                cx="1378.6867676"
                cy="326.6875916"
                r="9.4185133"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 79"
                fill="url(#SVGID_8_)"
                d="M1390.4680176,324.535675v-4.0259399
					c-0.4821777-0.0988464-1.0201416-0.0059814-1.5421143,0.396759c-9.4718018,7.2511902-17.876709,6.4422913-22.0205078,7.1417542
					v4.8536377C1374.7220459,332.3849182,1383.6671143,329.7840881,1390.4680176,324.535675z"
              />
              <radialGradient
                id="SVGID_9_"
                cx="1378.6867676"
                cy="333.6133118"
                r="9.412981"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 69"
                fill="url(#SVGID_9_)"
                d="M1390.4680176,331.5576172v-4.1331482
					c-1.3306885-0.1417847-2.9121094,1.4977722-4.5795898,2.4778137c-1.8966064,1.127594-4.6254883,2.3508301-7.5059814,3.195282
					c-5.4173584,1.6331787-9.1843262,1.6156006-11.4770508,1.8955688v4.817688
					C1376.0947266,339.1983948,1386.6409912,335.1051331,1390.4680176,331.5576172z"
              />
              <radialGradient
                id="SVGID_10_"
                cx="1378.6867676"
                cy="340.5063782"
                r="9.4180803"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 59"
                fill="url(#SVGID_10_)"
                d="M1388.9259033,334.7237549c-6.2403564,4.7803345-14.546875,6.9350891-22.0205078,7.1934814
					v4.8020935c8.161377-0.5391235,16.8825684-3.2036438,23.5626221-8.3639832v-4.0261536
					C1389.9852295,334.2315369,1389.4472656,334.3244019,1388.9259033,334.7237549z"
              />
              <radialGradient
                id="SVGID_11_"
                cx="1378.6867676"
                cy="347.4039001"
                r="9.4219141"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 49"
                fill="url(#SVGID_11_)"
                d="M1390.4680176,341.1879883c-1.1687012-0.1343689-2.5109863,1.315918-4.5795898,2.5317688
					c-5.7866211,3.4406738-12.5668945,4.6467285-18.9830322,5.094635v4.8140869
					c9.5031738-0.6319885,19.8066406-4.7467957,23.5626221-8.2685547V341.1879883z"
              />
              <radialGradient
                id="SVGID_12_"
                cx="1378.6867676"
                cy="354.33255"
                r="9.4128208"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 39"
                fill="url(#SVGID_12_)"
                d="M1370.8978271,360.0663452c7.1914063-1.089447,13.8841553-3.4720154,19.5701904-7.8803711
					v-4.0163574c-0.5091553-0.0952454-1.0612793,0.0032043-1.5421143,0.3716125
					c-2.7280273,2.0896301-5.8184814,3.8973389-10.543457,5.2825317c-5.4559326,1.6447449-9.2012939,1.63797-11.4770508,1.9055481
					v4.7991028C1368.1177979,360.5495911,1369.5587158,360.2714233,1370.8978271,360.0663452z"
              />
              <radialGradient
                id="SVGID_13_"
                cx="1378.6867676"
                cy="361.2337646"
                r="9.4184761"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 29"
                fill="url(#SVGID_13_)"
                d="M1388.9259033,355.4499512c-6.4416504,4.9346924-13.842041,6.6096191-22.0205078,7.1800842
					v4.8179016c0.369751-0.0271606,1.8809814-0.1493835,3.9924316-0.4728699
					c7.0001221-1.0604858,13.784668-3.3981323,19.5701904-7.8817444v-4.0379333
					C1389.9852295,354.9577332,1389.4472656,355.0505981,1388.9259033,355.4499512z"
              />
              <radialGradient
                id="SVGID_14_"
                cx="1378.8492432"
                cy="368.5759888"
                r="19.7340107"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 19"
                fill="url(#SVGID_14_)"
                d="M1388.9259033,362.3586731c-2.7767334,2.1269836-5.9595947,3.9389038-10.543457,5.2825317
					c-5.3966064,1.6270142-9.175293,1.6319885-11.4770508,1.9303284v4.7827148
					c7.881958-0.5211487,16.7843018-3.1359558,23.5626221-8.3661804v-4.0010071
					C1389.9588623,361.8918152,1389.4067383,361.9902649,1388.9259033,362.3586731z"
              />
              <radialGradient
                id="SVGID_15_"
                cx="1378.5493164"
                cy="374.8730469"
                r="23.6023083"
                gradientUnits="userSpaceOnUse"
              >
                <stop
                  offset="0"
                  style="stop-color:#97D645"
                />
                <stop
                  offset="1"
                  style="stop-color:#8AC43D"
                />
              </radialGradient>
              <path
                v-show="(schemeData.battery.meta?.stateOfCharge ?? 0) > 9"
                fill="url(#SVGID_15_)"
                d="M1390.4680176,372.8971863v-4.024353
					c-0.4827881-0.0976257-1.020752-0.0047913-1.5421143,0.3945618c-2.776123,2.1266174-5.9595947,3.9389038-10.543457,5.2825623
					c-5.4173584,1.6331787-9.1843262,1.6156006-11.4770508,1.8955383v4.8146973
					C1374.8479004,380.7162781,1383.6878662,378.1332092,1390.4680176,372.8971863z"
              />
            </g>
            <path
              fill="#5B5B5B"
              d="M1388.958252,305.184082v69.5082397c-5.9118652,4.8380432-14.317627,7.1237793-22.0528564,7.7306213
				v0.991394c8.1300049-0.4141235,17.2659912-2.6974792,23.5626221-7.8504333v-71.4724731
				C1389.9902344,304.4824219,1389.467041,304.8256531,1388.958252,305.184082z"
            />
          </g>

          <linearGradient
            id="SVGID_16_"
            gradientUnits="userSpaceOnUse"
            x1="1386.8648682"
            y1="309.0433044"
            x2="1369.6730957"
            y2="382.008667"
          >
            <stop
              offset="0"
              style="stop-color:#8ED7DB"
            />
            <stop
              offset="1"
              style="stop-color:#D4FCFF"
            />
          </linearGradient>
          <path
            opacity="0.2"
            fill="url(#SVGID_16_)"
            d="M1366.9053955,311.941864v71.4724731
			c8.1300049-0.4141235,17.2659912-2.6972656,23.5626221-7.8504333v-71.4724731
			c-3.2593994,2.6675415-7.279541,4.5658875-11.5273438,5.834259
			C1374.982666,311.1073914,1370.8269043,311.7421875,1366.9053955,311.941864z"
          />

          <linearGradient
            id="SVGID_17_"
            gradientUnits="userSpaceOnUse"
            x1="1361.2884521"
            y1="279.0690308"
            x2="1372.1252441"
            y2="279.0690308"
          >
            <stop
              offset="0"
              style="stop-color:#D6D6DD"
            />
            <stop
              offset="1"
              style="stop-color:#DFDFE5"
            />
          </linearGradient>
          <path
            opacity="0.9"
            fill="url(#SVGID_17_)"
            d="M1371.5344238,276.1923523
			c1.239624,0.7158508,0.5328369,3.0722656-2.1604004,4.6271667c-2.6931152,1.5549316-6.3526611,1.7194519-7.5924072,1.0038147
			c-1.239624-0.7158508-0.0615234-2.5565186,2.6315918-4.1114197
			C1367.1064453,276.1570129,1370.2945557,275.4766846,1371.5344238,276.1923523z"
          />
        </g>
        <g id="solar-panel-node">
          <polygon
            display="none"
            fill="#E5E5E5"
            points="767.1164551,206.4034729 864.0203857,122.5181351 969.4714355,183.984314
			872.5703125,267.8696594 		"
          />
          <g>
            <g>
              <g id="Axonometric_Cube_25_">
                <polygon
                  id="Cube_face_-_left_3_"
                  fill="#DBDBDB"
                  points="875.8112793,224.1658325 893.8065186,213.7762756
						893.8065186,205.3054962 875.8112793,215.6950378 					"
                />
                <polygon
                  id="Cube_face_-_right_3_"
                  fill="#EDEDED"
                  points="855.9902954,212.7223206 875.8112793,224.1658325
						875.8112793,215.6950378 855.9902954,204.2515259 					"
                />
                <polygon
                  id="Cube_face_-_top_3_"
                  fill="#F4F4F4"
                  points="875.8112793,215.6950378 893.8065186,205.3054962
						873.9855347,193.8619843 855.9902954,204.2515259 					"
                />
              </g>
            </g>
            <g>
              <g id="Axonometric_Cube_2_">
                <polygon
                  id="Cube_face_-_left_1_"
                  fill="#DBDBDB"
                  points="811.428833,186.9946747 829.4240112,176.6048126
						829.4240112,168.1340332 811.428833,178.5238953 					"
                />
                <polygon
                  id="Cube_face_-_right_1_"
                  fill="#EDEDED"
                  points="791.6078491,175.5508575 811.428833,186.9946747
						811.428833,178.5238953 791.6078491,167.0800629 					"
                />
                <polygon
                  id="Cube_face_-_top_1_"
                  fill="#F4F4F4"
                  points="811.428833,178.5238953 829.4240112,168.1340332
						809.6030884,156.6905212 791.6078491,167.0800629 					"
                />
              </g>
            </g>
            <g>
              <g>
                <g>
                  <g enable-background="new    ">
                    <polygon
                      fill="#48484D"
                      points="801.4396362,161.9871216 801.4400024,168.1589355 800.5820923,167.6604919
								800.5817871,161.488678 							"
                    />
                    <path
                      fill="#5F5F64"
                      d="M801.494873,157.5256805c0.5116577-1.1580048,1.2121582-2.2633514,2.0335083-3.1984558
								c0.6936035-0.7900848,1.4729614-1.4581451,2.2973633-1.9342041c1.4679565-0.8474579,2.7952271-0.927536,3.7533569-0.3707733
								l0.8578491,0.498764c-0.9578247-0.5567627-2.2854004-0.477005-3.7532959,0.3704529
								c-0.8244629,0.476059-1.6038208,1.1444397-2.2974243,1.9342041c-0.8212891,0.9351044-1.5218506,2.040451-2.0335083,3.1984558
								c-0.5791626,1.3105927-0.9165039,2.6883392-0.9130859,3.9629974l-0.8578491-0.4984436
								C800.5783691,160.2140198,800.9157104,158.8362732,801.494873,157.5256805z"
                    />
                    <path
                      fill="#3B3B3F"
                      d="M806.6836548,152.8914642c2.9168701-1.6838837,5.2798462-0.3373413,5.2893066,3.014328
								l0.0003662,6.171814l-10.5333252,6.0813293l-0.0003662-6.171814
								C801.4301758,158.635437,803.7780762,154.5690308,806.6836548,152.8914642z"
                    />

                    <linearGradient
                      id="SVGID_16_"
                      gradientUnits="userSpaceOnUse"
                      x1="1427.6829834"
                      y1="156.1756744"
                      x2="1430.5743408"
                      y2="156.1756744"
                      gradientTransform="matrix(-1 0 0 1 2232.0690918 0)"
                    >
                      <stop
                        offset="0"
                        style="stop-color:#27262A"
                      />
                      <stop
                        offset="0.5962505"
                        style="stop-color:#2F2F34"
                      />
                      <stop
                        offset="1"
                        style="stop-color:#37373D"
                      />
                    </linearGradient>
                    <path
                      fill="url(#SVGID_16_)"
                      d="M803.5283813,154.3272247l0.8578491,0.4984436
								c-0.8212891,0.9351044-1.5218506,2.040451-2.0335083,3.1984558l-0.8578491-0.4984436
								C802.0065308,156.3676758,802.7070313,155.2623291,803.5283813,154.3272247z"
                    />
                  </g>
                </g>
                <g enable-background="new    ">
                  <g>
                    <polygon
                      fill="#48484D"
                      points="808.6522217,87.2526245 802.7553101,83.8259125 802.7625732,167.0702972
								808.6591187,170.4970093 							"
                    />
                  </g>
                  <path
                    fill="#5F5F64"
                    d="M803.7619629,79.4618835c0.5668945-1.283165,1.3442993-2.5092621,2.2554321-3.5468369
							c0.7689819-0.8758316,1.6331177-1.6170349,2.546814-2.1444931c1.6183472-0.9341583,3.0843506-1.0208588,4.1430664-0.4057541
							l5.8965454,3.426712c-1.0586548-0.615097-2.5247192-0.5283966-4.1430054,0.4057617
							c-0.9136963,0.5277634-1.777832,1.2686615-2.546814,2.1444931c-0.9111328,1.0375671-1.6882935,2.2636719-2.2554321,3.5468369
							c-0.6384277,1.4452133-1.010498,2.962944-1.0063477,4.3640213l-5.8969116-3.426712
							C802.7515259,82.4245148,803.12323,80.9071045,803.7619629,79.4618835z"
                  />

                  <linearGradient
                    id="SVGID_17_"
                    gradientUnits="userSpaceOnUse"
                    x1="1420.1551514"
                    y1="79.401825"
                    x2="1428.307251"
                    y2="79.401825"
                    gradientTransform="matrix(-1 0 0 1 2232.0690918 0)"
                  >
                    <stop
                      offset="0"
                      style="stop-color:#27262A"
                    />
                    <stop
                      offset="0.5962505"
                      style="stop-color:#2F2F34"
                    />
                    <stop
                      offset="1"
                      style="stop-color:#37373D"
                    />
                  </linearGradient>
                  <path
                    fill="url(#SVGID_17_)"
                    d="M811.9140015,79.3417664l-5.8966064-3.4267197
							c-0.9111328,1.0375748-1.6885376,2.2636719-2.2554321,3.5468369l5.8966064,3.4267197
							C810.225708,81.6054382,811.0028687,80.3793335,811.9140015,79.3417664L811.9140015,79.3417664z"
                  />
                  <g>
                    <path
                      fill="#39393D"
                      d="M814.4608154,77.1972733c-3.2155151,1.8566437-5.8193359,6.3603363-5.8085938,10.0553513
								l0.006897,83.2443848l11.6503296-6.7263794l-0.0072632-83.2443771
								C820.2918091,76.8312378,817.6766357,75.3409348,814.4608154,77.1972733z"
                    />
                  </g>
                </g>
                <g enable-background="new    ">
                  <polygon
                    fill="#48484D"
                    points="810.7528687,167.3641205 810.7531738,173.5359344 809.8952637,173.0374908
							809.8950195,166.8653564 						"
                  />
                  <path
                    fill="#5F5F64"
                    d="M810.8077393,162.902359c0.5116577-1.1579895,1.2125244-2.2633514,2.0335083-3.1984558
							c0.6936035-0.7897491,1.4732666-1.4578247,2.2977295-1.9338837c1.4675903-0.8474579,2.7952271-0.927536,3.7533569-0.3707733
							l0.8578491,0.4984589c-0.9581299-0.556778-2.2857666-0.4766998-3.7533569,0.3707581
							c-0.8244629,0.476059-1.604126,1.1444397-2.2974243,1.9338989c-0.8212891,0.9354095-1.5217896,2.0407715-2.0338135,3.1984558
							c-0.5791626,1.3105927-0.9161987,2.6886597-0.9127197,3.9633026l-0.8578491-0.498764
							C809.8912354,165.5910187,810.2285767,164.2129669,810.8077393,162.902359z"
                  />
                  <path
                    fill="#3B3B3F"
                    d="M815.9968262,158.2684631c2.916626-1.6838837,5.2798462-0.3373413,5.2893066,3.0143433
							l0.0003662,6.1717987l-10.5333252,6.0813293l-0.0003052-6.171814
							C810.7434082,164.0124512,813.0912476,159.9460449,815.9968262,158.2684631z"
                  />

                  <linearGradient
                    id="SVGID_18_"
                    gradientUnits="userSpaceOnUse"
                    x1="1418.369751"
                    y1="161.5523682"
                    x2="1421.2614746"
                    y2="161.5523682"
                    gradientTransform="matrix(-1 0 0 1 2232.0690918 0)"
                  >
                    <stop
                      offset="0"
                      style="stop-color:#27262A"
                    />
                    <stop
                      offset="0.5962505"
                      style="stop-color:#2F2F34"
                    />
                    <stop
                      offset="1"
                      style="stop-color:#37373D"
                    />
                  </linearGradient>
                  <path
                    fill="url(#SVGID_18_)"
                    d="M812.8412476,159.7039032l0.8581543,0.4984589
							c-0.8212891,0.9354095-1.5217896,2.0407715-2.0338135,3.1984558l-0.8578491-0.4984589
							C811.319397,161.7443695,812.0202637,160.6390076,812.8412476,159.7039032z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g enable-background="new    ">
                    <polygon
                      fill="#48484D"
                      points="865.5853271,199.021759 865.5856934,205.193573 864.7277832,204.6951141
								864.727478,198.522995 							"
                    />
                    <path
                      fill="#5F5F64"
                      d="M865.640564,194.5599976c0.5116577-1.1580048,1.2121582-2.2633514,2.0335083-3.1984558
								c0.6936035-0.7897644,1.4729614-1.4578247,2.2973633-1.9338989c1.4676514-0.8474579,2.7952271-0.927536,3.7533569-0.3707581
								l0.8578491,0.4984436c-0.9578247-0.5567627-2.2854004-0.4766846-3.7532959,0.3707733
								c-0.8244629,0.476059-1.6038208,1.1444397-2.2974243,1.9338837
								c-0.8212891,0.9354248-1.5218506,2.0407715-2.0335083,3.1984558
								c-0.5791626,1.3105927-0.9165039,2.6886597-0.9130859,3.9633179l-0.8578491-0.498764
								C864.7240601,197.2486572,865.0614014,195.8705902,865.640564,194.5599976z"
                    />
                    <path
                      fill="#3B3B3F"
                      d="M870.8293457,189.9261017c2.9168701-1.6842041,5.2798462-0.3373413,5.2893066,3.014328
								l0.0003662,6.171814l-10.5333252,6.0813293l-0.0003662-6.171814
								C865.5758667,195.6700745,867.9237671,191.6036682,870.8293457,189.9261017z"
                    />

                    <linearGradient
                      id="SVGID_19_"
                      gradientUnits="userSpaceOnUse"
                      x1="1363.5372314"
                      y1="193.2099915"
                      x2="1366.4287109"
                      y2="193.2099915"
                      gradientTransform="matrix(-1 0 0 1 2232.0690918 0)"
                    >
                      <stop
                        offset="0"
                        style="stop-color:#27262A"
                      />
                      <stop
                        offset="0.5962505"
                        style="stop-color:#2F2F34"
                      />
                      <stop
                        offset="1"
                        style="stop-color:#37373D"
                      />
                    </linearGradient>
                    <path
                      fill="url(#SVGID_19_)"
                      d="M867.6740723,191.3615417l0.8578491,0.4984436
								c-0.8212891,0.9354248-1.5218506,2.0407715-2.0335083,3.1984558l-0.8578491-0.4984436
								C866.1522217,193.4019928,866.8527222,192.2966461,867.6740723,191.3615417z"
                    />
                  </g>
                </g>
                <g enable-background="new    ">
                  <g>
                    <polygon
                      fill="#48484D"
                      points="872.7976074,124.287262 866.901001,120.8602295 866.9082642,204.1046143
								872.8048096,207.5316467 							"
                    />
                  </g>
                  <path
                    fill="#5F5F64"
                    d="M867.9076538,116.496521c0.5668945-1.283165,1.3442993-2.5095825,2.2554932-3.5471497
							c0.7689819-0.8755188,1.6331177-1.6167297,2.546814-2.1441803c1.6182861-0.9341583,3.0842896-1.0211716,4.1430054-0.4057617
							l5.8965454,3.4267197c-1.0586548-0.6151047-2.5247192-0.5284042-4.1430054,0.4057541
							c-0.9136353,0.5274582-1.777832,1.2686691-2.546814,2.1441803c-0.9111328,1.0375671-1.6882935,2.2639847-2.2554321,3.5471497
							c-0.6384277,1.4452209-1.010437,2.9626312-1.0063477,4.3640289l-5.8969116-3.4270325
							C866.8972168,119.4591522,867.2689209,117.9417343,867.9076538,116.496521z"
                  />

                  <linearGradient
                    id="SVGID_20_"
                    gradientUnits="userSpaceOnUse"
                    x1="1356.0095215"
                    y1="116.4363022"
                    x2="1364.161499"
                    y2="116.4363022"
                    gradientTransform="matrix(-1 0 0 1 2232.0690918 0)"
                  >
                    <stop
                      offset="0"
                      style="stop-color:#27262A"
                    />
                    <stop
                      offset="0.5962505"
                      style="stop-color:#2F2F34"
                    />
                    <stop
                      offset="1"
                      style="stop-color:#37373D"
                    />
                  </linearGradient>
                  <path
                    fill="url(#SVGID_20_)"
                    d="M876.0596924,116.3764038l-5.8965454-3.4270325
							c-0.9111938,1.0375671-1.6885986,2.2639847-2.2554932,3.5471497l5.8966064,3.426712
							C874.3713989,118.6400681,875.1485596,117.4136505,876.0596924,116.3764038L876.0596924,116.3764038z"
                  />
                  <g>
                    <path
                      fill="#39393D"
                      d="M878.6065063,114.2319031c-3.2154541,1.8566513-5.8193359,6.3600311-5.8088989,10.0553589
								l0.0072021,83.2443848l11.6503296-6.7263794l-0.0072021-83.2443848
								C884.4375,113.8655548,881.8223267,112.3752518,878.6065063,114.2319031z"
                    />
                  </g>
                </g>
                <g enable-background="new    ">
                  <polygon
                    fill="#48484D"
                    points="874.8985596,204.3984528 874.8988647,210.5705719 874.0410156,210.0721283
							874.0407104,203.8999939 						"
                  />
                  <path
                    fill="#5F5F64"
                    d="M874.9534302,199.9369965c0.5116577-1.1580048,1.2125244-2.2633514,2.0335083-3.1984558
							c0.6936035-0.7897644,1.4732666-1.4581451,2.2977295-1.9342041c1.4675903-0.8474579,2.7952271-0.9272156,3.7533569-0.3704529
							l0.8575439,0.4984589c-0.9578247-0.556778-2.2854614-0.4766998-3.7530518,0.3707581
							c-0.8244629,0.4757538-1.604126,1.1441345-2.2974243,1.9338837c-0.8212891,0.9351044-1.5217896,2.0404663-2.0338135,3.1984711
							c-0.5791626,1.3105927-0.9165039,2.6886444-0.9127197,3.9629974l-0.8578491-0.4984589
							C874.0369263,202.6256561,874.3742676,201.2475891,874.9534302,199.9369965z"
                  />
                  <path
                    fill="#3B3B3F"
                    d="M880.1425171,195.3031006c2.916626-1.6842041,5.2798462-0.3373413,5.2893066,3.0140228
							l0.0003662,6.1721191l-10.5333252,6.0813293l-0.0003052-6.1721191
							C874.8890991,201.0470734,877.2366333,196.9806671,880.1425171,195.3031006z"
                  />

                  <linearGradient
                    id="SVGID_21_"
                    gradientUnits="userSpaceOnUse"
                    x1="1354.2241211"
                    y1="198.5869904"
                    x2="1357.1157227"
                    y2="198.5869904"
                    gradientTransform="matrix(-1 0 0 1 2232.0690918 0)"
                  >
                    <stop
                      offset="0"
                      style="stop-color:#27262A"
                    />
                    <stop
                      offset="0.5962505"
                      style="stop-color:#2F2F34"
                    />
                    <stop
                      offset="1"
                      style="stop-color:#37373D"
                    />
                  </linearGradient>
                  <path
                    fill="url(#SVGID_21_)"
                    d="M876.9869385,196.7385406l0.8581543,0.4984436
							c-0.8212891,0.9351044-1.5217896,2.0404663-2.0338135,3.1984711l-0.8578491-0.4984589
							C875.4650879,198.7789917,876.1659546,197.673645,876.9869385,196.7385406z"
                  />
                </g>
              </g>
            </g>
            <g>
              <g enable-background="new    ">
                <g>
                  <polygon
                    fill="#B8B8B8"
                    points="868.4407959,166.4671783 765.0529175,106.5016937 760.0466919,89.5935364
							863.4342651,149.559021 						"
                  />
                </g>
                <polygon
                  fill="#F4F4F4"
                  points="806.2507324,49.6069908 760.0466919,89.5935364 863.4342651,149.559021
						909.6386108,109.572464 					"
                />
                <polygon
                  fill="#345E7A"
                  points="762.2280884,89.1609802 806.2548828,51.049057 907.1577148,109.5705719
						863.1322021,147.6824951 					"
                />
                <g>
                  <polygon
                    fill="#C7C7C7"
                    points="909.6386108,109.572464 914.6448975,126.4806213 868.4407959,166.4671783
							863.4342651,149.559021 						"
                  />
                </g>
              </g>
            </g>
            <g>
              <g>
                <g enable-background="new    ">
                  <g>
                    <polygon
                      fill="#EDEDED"
                      points="843.0690918,173.6270447 732.1486816,108.9829025 731.1461182,105.5599747
								842.0665283,170.2044373 							"
                    />
                  </g>
                  <polygon
                    fill="#F4F4F4"
                    points="832.8422241,17.5492764 731.1461182,105.5602875 842.0665283,170.2041168
							943.7630005,82.1931076 						"
                  />
                  <polygon
                    fill="#345E7A"
                    points="735.9474487,104.6081619 832.8513794,20.722826 938.3024292,82.1890106
							841.4013062,166.0743408 						"
                  />
                  <g>
                    <polygon
                      fill="#DBDBDB"
                      points="943.7630005,82.1934204 944.7658691,85.6163559 843.0690918,173.6270447
								842.0665283,170.2044373 							"
                    />
                  </g>
                </g>
              </g>
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M901.131897,96.1301193c0.5164185,0,0.9874268,0.115387,1.3626099,0.3326111
					l7.4644165,4.326828c0.4256592,0.2462311,0.6797485,0.5993347,0.7147827,0.9946899
					c0.0393677,0.428772-0.175293,0.8651123-0.6040649,1.2295685l-6.9524536,5.9255829
					c-0.5369263,0.4565125-1.3260498,0.7292252-2.1135864,0.7292252c-0.5223999,0-0.9994507-0.1150742-1.3771362-0.3354492
					l-7.4656982-4.3252487c-0.4174194-0.2421341-0.6671143-0.5879898-0.7020874-0.9729385
					c-0.0394287-0.4316101,0.1837769-0.8780365,0.6289673-1.2576294l6.9523926-5.9240036
					C899.5709839,96.3996811,900.3528442,96.1301193,901.131897,96.1301193z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M876.789917,82.0297928c0.5233154,0,1.0018921,0.1163406,1.3808594,0.3370285
					l7.4644775,4.3252487c0.4202271,0.2437057,0.6696167,0.5905075,0.7045898,0.9786072
					c0.0394287,0.4287796-0.1777954,0.8679504-0.6125488,1.236824l-6.9540405,5.9240036
					c-0.5413208,0.4625092-1.3313599,0.7383728-2.1132813,0.7383728h-0.0018921
					c-0.5119629,0-0.9801636-0.1150742-1.3553467-0.3326187l-7.4656982-4.3252411
					c-0.4230957-0.2462311-0.6765747-0.5980759-0.711853-0.990593c-0.0391235-0.4331894,0.1809692-0.8783569,0.6201172-1.2516403
					l6.9524536-5.9240036C875.2245483,82.2980957,876.0061035,82.0297928,876.789917,82.0297928z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M888.949707,89.0786972c0.5252686,0,1.0038452,0.1150742,1.3828125,0.3351364
					l7.4643555,4.3268204c0.4263306,0.2465439,0.6801147,0.5996552,0.7150879,0.9946899
					c0.0394287,0.428772-0.1749878,0.8666916-0.6040649,1.2314606l-6.9524536,5.9236908
					c-0.5349731,0.4580917-1.3259888,0.7311172-2.1135864,0.7311172c-0.5223999,0-0.9993896-0.1166458-1.3770752-0.3354492
					l-7.4660034-4.3271408c-0.4227905-0.2449646-0.6769409-0.5964966-0.7116089-0.9902725
					c-0.0397339-0.4316101,0.1806641-0.8767776,0.6195068-1.250061l6.9527588-5.9255829
					C887.3843994,89.3454208,888.1665649,89.0786972,888.949707,89.0786972z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M810.0570679,94.0307083h-0.0012817c-0.520813,0-0.9949951-0.1150742-1.3682861-0.3310394
					l-7.4647217-4.3265076c-0.4143066-0.239296-0.6608276-0.5851517-0.6958008-0.9716721
					c-0.0394897-0.4306641,0.1881714-0.8881302,0.6213989-1.2576294l6.9539795-5.9255829
					c0.5296631-0.4517899,1.3115234-0.7216644,2.090271-0.7216644c0.5164185,0,0.9874268,0.1150818,1.3626099,0.3322983
					l7.4660034,4.3252487c0.4243774,0.2465439,0.6781616,0.5996552,0.7144165,0.9953232
					c0.038147,0.428772-0.1762085,0.8660583-0.6049805,1.2308273l-6.9524536,5.9240036
					C811.6453857,93.7595749,810.8521729,94.0307083,810.0570679,94.0307083z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M822.2232056,101.0824432c-0.5192871,0-0.9950562-0.115387-1.3743286-0.3354492
					l-7.4644165-4.3252487c-0.4146118-0.2405548-0.6608276-0.5864105-0.6958618-0.9732513
					c-0.0380859-0.4300308,0.1882935-0.8881226,0.62146-1.2569962l6.9536743-5.9243164
					c0.5309448-0.4524231,1.3134766-0.7235565,2.0921631-0.7235565c0.5148315,0,0.9862061,0.1153946,1.3610229,0.3322983
					l7.4660645,4.326828c0.4302979,0.2496948,0.6856689,0.6012268,0.7207031,0.9921646
					c0.0393677,0.4259338-0.1781616,0.863533-0.6113281,1.2330399l-6.9527588,5.9249496
					C823.8026733,100.8097382,823.0123291,101.0824432,822.2232056,101.0824432z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M864.6218872,74.9764786c0.5220337,0,1.0022583,0.1182251,1.3875122,0.3414459
					l7.4644165,4.3265076c0.418396,0.2424469,0.6693726,0.589241,0.7046509,0.975769
					c0.0390625,0.428772-0.1781006,0.8695297-0.612854,1.2390289l-6.9537354,5.9252625
					c-0.5413208,0.4609299-1.3320313,0.7364807-2.1170654,0.7364807c-0.5116577,0-0.9811401-0.1150742-1.3547363-0.3310394
					l-7.4647217-4.3268204c-0.4290771-0.2478027-0.6838379-0.5993347-0.718811-0.9887009
					c-0.038147-0.4306641,0.1837769-0.8751984,0.6270752-1.2519531l6.9523926-5.9255753
					C863.0656738,75.2463531,863.8460083,74.9764786,864.6218872,74.9764786z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M838.5978394,110.5791397c-0.5119629,0-0.9788818-0.112236-1.3483887-0.3272552
					l-7.4656982-4.3265076c-0.4243164-0.2449722-0.6768799-0.5965042-0.711853-0.987442
					c-0.0375366-0.4271927,0.1869507-0.8852921,0.6201172-1.2528992l6.9524536-5.9243164
					c0.5292969-0.4524231,1.3083496-0.7204056,2.0861206-0.7204056c0.5224609,0,1.0019531,0.1166534,1.3869019,0.3398666
					l7.4644775,4.3265076c0.4202271,0.2437057,0.6696167,0.5908279,0.7045898,0.977356
					c0.0397339,0.4275055-0.1774902,0.8679428-0.6125488,1.2374496l-6.9524536,5.9252625
					C840.1834106,110.3045349,839.3898315,110.5791397,838.5978394,110.5791397z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M848.2471924,65.4810486c0.5151367,0,0.9864502,0.1157074,1.3613281,0.3332443
					l7.4644165,4.3265076c0.4300537,0.249382,0.6870117,0.6021729,0.7219849,0.9934311
					c0.0394287,0.4243622-0.1777954,0.8619614-0.6113281,1.2311478l-6.9523926,5.9255753
					c-0.5394287,0.4593582-1.3273315,0.7336426-2.1091919,0.7336426c-0.5192261,0-0.9974976-0.1166458-1.3815308-0.339859
					l-7.4656982-4.3252487c-0.4174194-0.2421341-0.6668091-0.5879898-0.7021484-0.9732513
					c-0.0390625-0.4316101,0.1841431-0.8780441,0.6290283-1.2570038l6.9526978-5.9243164
					C846.6849976,65.7525024,847.4681396,65.4810486,848.2471924,65.4810486z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M833.9926758,59.1563339c0.5306396-0.4521027,1.3140869-0.7238693,2.0931396-0.7238693
					c0.5148315,0,0.986145,0.1153908,1.3607178,0.3329277l7.4644165,4.3265076
					c0.4262695,0.2465439,0.6800537,0.5993385,0.7150269,0.9950066c0.0394287,0.428772-0.1749878,0.8666916-0.6037598,1.2311478
					l-6.9526978,5.9240036c-0.5350342,0.4584045-1.3257446,0.7308044-2.1139526,0.7308044
					c-0.5220947,0-0.9991455-0.1166534-1.37677-0.3354492l-7.4660034-4.326828
					c-0.4171143-0.2421265-0.6665039-0.5876694-0.7015381-0.9716721c-0.0393677-0.4331894,0.1838379-0.8796158,0.6290283-1.2570038
					L833.9926758,59.1563339z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M822.647522,68.8232727c0.5413208-0.4609299,1.333313-0.7364807,2.1167603-0.7364807
					c0.5117188,0,0.9817505,0.115387,1.3550415,0.3313522l7.4647827,4.3265076
					c0.4243164,0.2449722,0.6765747,0.5980759,0.7131348,0.990593c0.0378418,0.4331894-0.1809692,0.8764648-0.6201782,1.250061
					l-6.9536743,5.9240036c-0.5252686,0.4492645-1.3071289,0.7159882-2.090271,0.7159882
					c-0.5239868,0-1.0022583-0.1150742-1.3815308-0.3354492l-7.4660034-4.3265076
					c-0.4187012-0.2421341-0.6696167-0.5892487-0.7046509-0.9757767c-0.0377808-0.428772,0.1794434-0.8682632,0.6141968-1.2387161
					L822.647522,68.8232727z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M851.6287842,134.336319c-0.5255127,0-1.003479-0.1150818-1.3830566-0.3354645
					l-7.4656982-4.3265076c-0.4246826-0.2468567-0.6784668-0.5996399-0.7147827-0.9953156
					c-0.038147-0.428772,0.1765747-0.8663712,0.6053467-1.2311478l6.9523926-5.9236832
					c0.5354004-0.4584122,1.3261108-0.7308044,2.1136475-0.7308044c0.5223999,0,0.9993896,0.1166458,1.3771362,0.3354492
					l7.4644165,4.3265076c0.4243774,0.245285,0.6771851,0.5983887,0.7134399,0.9909058
					c0.038147,0.4331894-0.1809692,0.8764648-0.619812,1.250061l-6.9527588,5.9252625
					C853.1941528,134.0692749,852.4119263,134.336319,851.6287842,134.336319z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M863.7905273,141.3852081h-0.0015869
					c-0.5236816,0-1.0019531-0.1166382-1.3812256-0.3370209l-7.4660645-4.3252411
					c-0.4186401-0.2437134-0.6695557-0.5908356-0.7045898-0.9776764c-0.038147-0.4272003,0.1793823-0.86763,0.6141357-1.2380829
					l6.9527588-5.9243164c0.5407104-0.4606171,1.3314209-0.7364807,2.1148682-0.7364807
					c0.5136108,0,0.9817505,0.1138153,1.3569336,0.3310394l7.4656982,4.326828
					c0.4227905,0.2449646,0.675293,0.5964966,0.7122192,0.9890137c0.0387573,0.4331818-0.1812744,0.8783569-0.6201782,1.2519531
					l-6.9523926,5.9233704C865.354248,141.1166077,864.5723877,141.3852081,863.7905273,141.3852081z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M836.8005371,88.6786118c-0.5224609,0-1.0023193-0.1182251-1.3875732-0.3414383
					l-7.4656372-4.3265076c-0.418396-0.2424469-0.6696777-0.5895691-0.7046509-0.9760895
					c-0.0378418-0.4287796,0.1793823-0.8679504,0.6144409-1.2384033l6.9521484-5.9240036
					c0.5412598-0.4621887,1.3320313-0.7380524,2.1151733-0.7380524c0.5135498,0,0.9817505,0.1150742,1.3566284,0.3326111
					l7.4644165,4.3252487c0.4293823,0.2481232,0.6844482,0.5993347,0.7210083,0.9890137
					c0.0378418,0.4300385-0.184082,0.875206-0.6273804,1.2532196l-6.9543457,5.9236832
					C838.3573608,88.4087372,837.576355,88.6786118,836.8005371,88.6786118z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M865.4664917,91.6834946c0.5223999,0,0.9994507,0.1156998,1.3771362,0.335762
					l7.4647217,4.3249359c0.4168091,0.2421265,0.6661987,0.5883026,0.7011719,0.9732513
					c0.0394287,0.4316101-0.1837769,0.8780365-0.6282959,1.257309l-6.9524536,5.924324
					c-0.53125,0.4521027-1.3146973,0.7232361-2.0934448,0.7232361c-0.5151367,0-0.986145-0.1150742-1.3610229-0.3322983
					l-7.4644165-4.326828c-0.4246826-0.2465439-0.6781616-0.5996475-0.7150269-0.9934235
					c-0.0388184-0.428772,0.1749268-0.8666916,0.6036987-1.2314606l6.9543457-5.9252701
					C863.8895264,91.9565201,864.6789551,91.6834946,865.4664917,91.6834946z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M853.300415,84.6304932c0.520813,0,0.9981079,0.1182251,1.3815308,0.3398666
					l7.4644165,4.3268204c0.4174194,0.2421341,0.6665039,0.5879898,0.7017822,0.9716721
					c0.0391235,0.4331894-0.184082,0.8796158-0.6286621,1.2573166l-6.9523926,5.9255829
					c-0.53125,0.4521027-1.3128052,0.7219772-2.0921631,0.7219772c-0.5161133,0-0.987793-0.1153946-1.3623047-0.3326187
					l-7.4644165-4.3252487c-0.4303589-0.2496948-0.68573-0.6024857-0.7219849-0.9934235
					c-0.038147-0.425621,0.1793823-0.8635406,0.6109619-1.2327271l6.9540405-5.9240036
					C851.730957,84.9047775,852.5197754,84.6304932,853.300415,84.6304932z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M877.6282349,98.7308197c0.5223999,0,0.9993896,0.1166458,1.3770752,0.335762
					l7.4644775,4.3265076c0.4246216,0.245285,0.6768799,0.5965042,0.711853,0.990593
					c0.0394287,0.4319305-0.1793823,0.8767776-0.6185303,1.250061l-6.9537354,5.9240036
					c-0.5252075,0.4492645-1.3071289,0.7166214-2.0905762,0.7166214c-0.5236816,0-1.0022583-0.1153946-1.3812256-0.3357697
					l-7.4663086-4.3265076c-0.4240112-0.2468567-0.6781616-0.5996475-0.7144165-0.9953232
					c-0.038147-0.428772,0.1762695-0.8663712,0.6053467-1.2308273l6.9523926-5.9240036
					C876.0499268,99.0035324,876.840332,98.7308197,877.6282349,98.7308197z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M889.8104248,105.7825546c0.5135498,0,0.9817505,0.1153946,1.3566284,0.3326187
					l7.4644165,4.3252411c0.4246826,0.2465439,0.6768799,0.5980759,0.7131348,0.9918518
					c0.038147,0.4319305-0.1806641,0.8755188-0.6195068,1.2488022l-6.9543457,5.9255829
					c-0.5249023,0.4480057-1.3051758,0.7147293-2.0886841,0.7147293c-0.5249023,0-1.0037842-0.1153946-1.3830566-0.3354568
					l-7.4656982-4.3268204c-0.4190063-0.2418137-0.6696777-0.5895615-0.7046509-0.9760895
					c-0.0375366-0.428772,0.1793823-0.8676376,0.6144409-1.2380829l6.9521484-5.9243164
					C888.2365723,106.0581055,889.0272827,105.7825546,889.8104248,105.7825546z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M772.7364502,91.3335342c0.0388184,0.4306641-0.1869507,0.8897095-0.6204834,1.2588959
					l-6.9536133,5.9255753c-0.53125,0.4517899-1.3131714,0.7219772-2.0921631,0.7219772
					c-0.5148926,0-0.9859009-0.1150742-1.361084-0.3326111l-7.4656982-4.3252487
					c-0.4302979-0.249382-0.6856689-0.6024857-0.7207031-0.9931107c-0.0394287-0.4259415,0.1777954-0.8641663,0.6113281-1.2327271
					l6.9520874-5.9243164c0.5372314-0.4577789,1.3288574-0.7304916,2.1186523-0.7304916
					c0.5189819,0,0.993103,0.1166534,1.3723755,0.3354492l7.4644775,4.3265152
					C772.4545898,90.6027298,772.7008667,90.9470139,772.7364502,91.3335342z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M862.9414673,124.6794662c-0.5223999,0-0.9946899-0.1153946-1.367981-0.3313599
					l-7.4666138-4.3265076c-0.4180908-0.2424469-0.6693115-0.5936584-0.7040405-0.9874344
					c-0.0396729-0.4319305,0.1838379-0.888443,0.6126099-1.2532196l6.9520874-5.9243164
					c0.5268555-0.4489517,1.3087158-0.7159882,2.0934448-0.7159882c0.5239868,0,1.0009766,0.1166534,1.380249,0.3354492
					l7.4660034,4.326828c0.4243774,0.2462311,0.6781616,0.5993347,0.7144165,0.9946899
					c0.038147,0.428772-0.1762085,0.8670044-0.6049805,1.2311478l-6.9527588,5.9243164
					C864.5314331,124.4080124,863.7369385,124.6794662,862.9414673,124.6794662z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M875.1044312,131.7267914c-0.5220947,0-0.9962769-0.1138153-1.3695679-0.3310394
					l-7.4644165-4.3252487c-0.4145508-0.241188-0.6608276-0.5848312-0.6958008-0.9719925
					c-0.0394287-0.4300308,0.1863403-0.8897018,0.6213989-1.2585754l6.9539795-5.9240036
					c0.5309448-0.4524155,1.3131714-0.7238693,2.0915527-0.7238693c0.5148315,0,0.9865112,0.1150742,1.3613892,0.3326187
					l7.4656372,4.3271332c0.4246826,0.2462311,0.6781616,0.5993423,0.7150879,0.9931183
					c0.0374756,0.428772-0.1765747,0.8666916-0.6056519,1.2314606l-6.9524536,5.9255753
					C876.6934204,131.4550171,875.8995361,131.7267914,875.1044312,131.7267914z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M839.4453735,127.2845764c-0.5151978,0-0.9862061-0.1150742-1.3613892-0.3326187
					l-7.4656982-4.3252487c-0.4243164-0.2462234-0.6784668-0.5993347-0.7147217-0.9946899
					c-0.038147-0.428772,0.1765747-0.8663712,0.6053467-1.2311401l6.9523926-5.9255829
					c0.5369263-0.4565201,1.3260498-0.7292252,2.1136475-0.7292252c0.5223999,0,0.9993896,0.115387,1.3770752,0.3354492
					l7.4644775,4.3252487c0.4189453,0.2437057,0.668335,0.5892487,0.7033691,0.9745102
					c0.0374756,0.4316101-0.1850586,0.8780365-0.6289673,1.2557373l-6.9537354,5.9243164
					C841.0059814,127.0150146,840.225647,127.2845764,839.4453735,127.2845764z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M850.7595825,117.6277237c-0.5123291,0-0.979248-0.1138153-1.3480835-0.3282013
					l-7.4660034-4.3252487c-0.418396-0.2437057-0.6696777-0.5952377-0.7046509-0.9890137
					c-0.0394287-0.4300308,0.1837769-0.8868637,0.6125488-1.2519531l6.9527588-5.9255829
					c0.5249634-0.4476852,1.3068237-0.7144089,2.090271-0.7144089c0.5249023,0,1.0038452,0.1150742,1.3831177,0.3357697
					l7.4644165,4.3265076c0.4202881,0.2437057,0.6712036,0.5895615,0.7059326,0.9760895
					c0.038147,0.428772-0.1794434,0.8679504-0.6141968,1.2383957l-6.9520874,5.9236908
					C852.3453979,117.3537521,851.5499878,117.6277237,850.7595825,117.6277237z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M836.3818359,37.8305511c-0.5309448,0.4524193-1.3146973,0.7235565-2.0934448,0.7235565
					c-0.5145264,0-0.9858398-0.1153908-1.3607178-0.3326149l-7.4660034-4.3265076
					c-0.4306641-0.2496986-0.68573-0.6028061-0.7207031-0.9937439c-0.0394287-0.425621,0.1781006-0.8635368,0.6113281-1.2324085
					l6.9523926-5.9243183c0.5366211-0.4580936,1.3289185-0.7308044,2.118042-0.7308044
					c0.5195313,0,0.9949951,0.1169662,1.3726807,0.3357658l7.4660034,4.3265095
					c0.4130249,0.2392921,0.6592407,0.5835724,0.6942749,0.9700985c0.0393677,0.4303493-0.1866455,0.8900185-0.6210938,1.2592049
					L836.3818359,37.8305511z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M860.7241211,51.9381294c-0.5249023,0.4480057-1.3067627,0.7150421-2.09021,0.7150421
					c-0.5249634,0-1.0038452-0.1150742-1.3831177-0.3357658l-7.4644775-4.3265114
					c-0.4202271-0.2437057-0.6696167-0.5905075-0.7045898-0.9773483c-0.0394287-0.4275131,0.1781006-0.8679504,0.612854-1.2371368
					l6.9524536-5.9255791c0.5378418-0.4580917,1.3317261-0.7320671,2.1236572-0.7320671
					c0.5123291,0,0.979248,0.1122398,1.3481445,0.3269386l7.4656982,4.3265114
					c0.4189453,0.2418137,0.6699219,0.5936584,0.7049561,0.9874382c0.0393677,0.4316101-0.1838379,0.888443-0.6126099,1.2528992
					L860.7241211,51.9381294z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M848.5435181,44.8794556c-0.5308838,0.4524155-1.3128052,0.7219772-2.0918579,0.7219772
					c-0.5164185,0-0.9874268-0.1150742-1.3626099-0.3326149l-7.4644165-4.3252487
					c-0.4255981-0.2465439-0.6797485-0.6009102-0.7147217-0.9962654c-0.0394287-0.4290886,0.1749878-0.8651123,0.6037598-1.2298851
					l6.9527588-5.9255791c0.5324707-0.4536781,1.3256836-0.7248154,2.1208496-0.7248154
					c0.5220947,0,0.9962158,0.1138153,1.369812,0.3310394l7.4663696,4.3252449
					c0.4123535,0.239296,0.6589355,0.5851517,0.6939087,0.9713593c0.0394287,0.4306679-0.1869507,0.8903351-0.6213989,1.2592087
					L848.5435181,44.8794556z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M872.8858643,58.9854546c-0.5293579,0.45084-1.3099976,0.7210312-2.0861816,0.7210312
					c-0.5220947,0-1.0016479-0.1185417-1.3869019-0.3417549l-7.4644165-4.3252487
					c-0.4205933-0.2433929-0.6699829-0.5908241-0.7049561-0.978611c-0.0394287-0.4275131,0.1781006-0.8666878,0.6125488-1.2371368
					l6.9543457-5.9240036c0.5366211-0.4584084,1.3301392-0.7320671,2.1224365-0.7320671
					c0.5116577,0,0.9785767,0.1122398,1.3477783,0.3263092l7.4660034,4.3268242
					c0.4227905,0.2449684,0.6768799,0.5952377,0.7119141,0.9861794c0.0393677,0.428772-0.1869507,0.8856049-0.6201782,1.2544746
					L872.8858643,58.9854546z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M913.6101685,82.5868835c-0.5249634,0.4476929-1.3071289,0.7150421-2.090271,0.7150421
					c-0.5256348,0-1.0038452-0.115387-1.3831787-0.3357697l-7.4643555-4.3268204
					c-0.4259644-0.2462311-0.6797485-0.5993347-0.7147827-0.9946899c-0.0393677-0.428772,0.1749878-0.8663712,0.6040649-1.2311478
					l6.9524536-5.9240036c0.5324707-0.4552536,1.3276367-0.7267075,2.1221313-0.7267075
					c0.5223999,0,0.9953003,0.1153946,1.3685913,0.3313522l7.4656982,4.326828
					c0.4190063,0.2421265,0.6699219,0.5933456,0.7049561,0.9877548c0.0393677,0.4312897-0.1838379,0.8881226-0.6132202,1.2525787
					L913.6101685,82.5868835z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M901.4295044,75.5278931c-0.5308838,0.4521027-1.3128052,0.72229-2.0917969,0.72229
					c-0.5164185,0-0.9874878-0.1150742-1.3626709-0.3326111l-7.4660034-4.3252487
					c-0.4240112-0.2468567-0.6781616-0.5996475-0.7144165-0.9950027c-0.038147-0.428772,0.1762695-0.8666916,0.6053467-1.2311478
					l6.9520874-5.9258919c0.5328369-0.4533653,1.3260498-0.7245026,2.1212158-0.7245026
					c0.5220947,0,0.9962158,0.1134987,1.369873,0.3307228l7.4656372,4.3255615
					c0.4130249,0.239296,0.6596069,0.5851517,0.6945801,0.9716797c0.0393677,0.4300308-0.1869507,0.8897018-0.6213989,1.2585754
					L901.4295044,75.5278931z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M889.2665405,68.4793091c-0.531189,0.4517899-1.3134155,0.7232361-2.0921631,0.7232361
					c-0.5151367,0-0.9859009-0.1150742-1.3607178-0.3326111l-7.4663086-4.3265076
					c-0.4303589-0.249382-0.6854248-0.6024933-0.7203979-0.9931183c-0.0394287-0.4262505,0.1777954-0.8638496,0.6110229-1.2327194
					l6.9526978-5.9243202c0.5366211-0.4580917,1.3288574-0.7308044,2.118042-0.7308044
					c0.5195313,0,0.9934082,0.1169662,1.3726807,0.3357658l7.4644165,4.3265076
					c0.4146118,0.239296,0.6608276,0.5835724,0.6958008,0.9701004c0.0394287,0.4300346-0.1865845,0.8897057-0.6213379,1.2588882
					L889.2665405,68.4793091z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M809.9621582,59.6201019c0.0397339,0.4331856-0.1837769,0.8796158-0.6286621,1.2573128
					l-6.9523926,5.9252663c-0.53125,0.4524155-1.3131714,0.7226028-2.0922241,0.7226028
					c-0.5160522,0-0.9870605-0.115387-1.3622437-0.3326111l-7.4644775-4.3252449
					c-0.4302979-0.2496986-0.6856689-0.6024895-0.7219238-0.9937477c-0.038147-0.4256172,0.1793823-0.8632202,0.6109619-1.23209
					l6.9543457-5.9243202c0.5393677-0.4612427,1.3288574-0.7349014,2.1091919-0.7349014
					c0.520813,0,0.9978027,0.1179123,1.3811646,0.33955l7.4644775,4.3268242
					C809.6777344,58.8905563,809.9274902,59.2348366,809.9621582,59.6201019z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M784.0721436,81.6836243c0.038147,0.4319229-0.1838379,0.8783493-0.6286621,1.2563629
					l-6.9539795,5.9252701c-0.5309448,0.4524155-1.3128052,0.7235489-2.0934448,0.7235489
					c-0.5132446,0-0.9845581-0.1150742-1.3594971-0.3326111l-7.4659424-4.3265076
					c-0.4303589-0.249382-0.6854248-0.6024933-0.7203979-0.9937439c-0.0394287-0.4259338,0.1777954-0.8632202,0.6113281-1.2324066
					l6.9520874-5.9240036c0.5397339-0.4609299,1.3291626-0.7352219,2.1094971-0.7352219
					c0.5205078,0,0.9974976,0.1182327,1.3815308,0.3395538l7.4644165,4.3271332
					C783.7877808,80.9528198,784.0371704,81.2986755,784.0721436,81.6836243z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M795.4005737,72.0355988c0.0397339,0.4275131-0.1774902,0.8679504-0.6125488,1.2371368
					l-6.9521484,5.9252625c-0.5384521,0.4580994-1.3320313,0.7320709-2.1239624,0.7320709
					c-0.5120239,0-0.9789429-0.1119232-1.3478394-0.3263092l-7.4663086-4.326828
					c-0.4243774-0.2449646-0.6765747-0.5964966-0.711853-0.9871216c-0.0375977-0.4278259,0.1869507-0.8859177,0.6201172-1.2535324
					l6.9524536-5.923996c0.5292969-0.4521103,1.3083496-0.7204056,2.0861206-0.7204056
					c0.5224609,0,1.0019531,0.116333,1.3869019,0.3395538l7.4644165,4.3268204
					C795.1162109,71.3019562,795.3656006,71.6474991,795.4005737,72.0355988z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M775.2321777,106.288887c-0.5148315,0-0.986145-0.1153946-1.3610229-0.3326187
					l-7.4660034-4.3265076c-0.4246826-0.2468567-0.6781616-0.599968-0.7150269-0.9937439
					c-0.0375366-0.428772,0.1768188-0.866684,0.6055908-1.2311478l6.9524536-5.9252625
					c0.5328369-0.4539948,1.3260498-0.7251282,2.1211548-0.7251282c0.5220947,0,0.9962769,0.1138153,1.3695679,0.3313522
					l7.4647217,4.3252487c0.4127197,0.2389755,0.6605225,0.5848312,0.6954956,0.9713593
					c0.038147,0.4300308-0.1879272,0.8897018-0.6210938,1.2585754l-6.9539795,5.9243164
					C776.7927856,106.0174332,776.0112305,106.288887,775.2321777,106.288887z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M832.6196899,40.3221664c0.0393677,0.4275093-0.1778564,0.8666878-0.6126099,1.2355576
					l-6.9543457,5.9252663c-0.5365601,0.4584084-1.3301392,0.7323799-2.1220703,0.7323799
					c-0.5120239,0-0.9789429-0.112236-1.3477783-0.326622l-7.4656982-4.3265114
					c-0.4249878-0.2449646-0.6772461-0.5968132-0.7122192-0.987751c-0.0378418-0.4275131,0.1866455-0.8852921,0.619812-1.2532158
					l6.9523926-5.923687c0.5293579-0.4508438,1.3103027-0.721035,2.0861816-0.721035
					c0.5224609,0,1.0022583,0.1185455,1.3875732,0.3417587l7.4644165,4.3252487
					C832.3352661,39.5869446,832.5846558,39.934063,832.6196899,40.3221664z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M821.2924805,49.9733391c0.0397339,0.4275131-0.1777954,0.8676338-0.612915,1.2371368
					l-6.9536743,5.9255791c-0.5413208,0.4609299-1.3320313,0.7361641-2.1154785,0.7361641
					c-0.5120239,0-0.9814453-0.1150742-1.3550415-0.3310356l-7.4660034-4.3265076
					c-0.428772-0.2478065-0.6838379-0.5996513-0.718811-0.9890175c-0.038208-0.4303474,0.1837769-0.8752022,0.62677-1.2516365
					l6.9526978-5.9252663c0.5296631-0.4511566,1.3099976-0.7207146,2.0858765-0.7207146
					c0.5220947,0,1.0022583,0.1179123,1.3875122,0.3411255l7.4644165,4.3265076
					C821.0078125,49.2393799,821.2575073,49.5864983,821.2924805,49.9733391z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M925.7718506,89.6342087c-0.5268555,0.4480057-1.3084106,0.7159882-2.0921631,0.7159882
					c-0.5233765,0-1.0019531-0.116333-1.3809204-0.3367157l-7.4644165-4.3252411
					c-0.4202881-0.2433929-0.6699829-0.5911407-0.7049561-0.9789276c-0.0394287-0.4272003,0.1781616-0.8666916,0.6126099-1.236824
					l6.9523926-5.9243164c0.5385132-0.4577789,1.3320313-0.7323837,2.1243286-0.7323837
					c0.5116577,0,0.9786377,0.1125565,1.3474731,0.3269424l7.4663086,4.3265076
					c0.418335,0.2424469,0.6693726,0.5939713,0.7043457,0.9877548c0.0394287,0.4319229-0.1837769,0.8881226-0.6126099,1.253212
					L925.7718506,89.6342087z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M774.1016235,110.1907196c0.538147-0.4577789,1.3313599-0.7320633,2.1236572-0.7320633
					c0.5123291,0,0.9789429,0.1125488,1.3480835,0.3269348l7.4644775,4.3265076
					c0.4202881,0.2437057,0.6696167,0.5936661,0.7058716,0.9893341c0.038147,0.4300308-0.1837769,0.8865509-0.6125488,1.2513199
					l-6.9539795,5.9240036c-0.5322266,0.4552536-1.3258057,0.7263947-2.1208496,0.7263947h-0.0015869
					c-0.520874,0-0.9934692-0.1150742-1.3670654-0.3310394l-7.4660034-4.3268204
					c-0.4123535-0.2389832-0.6589355-0.5832596-0.6939087-0.9701004c-0.0394287-0.4303513,0.1869507-0.8897018,0.6213989-1.2588882
					L774.1016235,110.1907196z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M826.9683838,140.828125c0.5324707-0.4536743,1.3262939-0.7251282,2.12146-0.7251282
					c0.5220947,0,0.9959717,0.1150665,1.3695679,0.3313446l7.4644165,4.3265076
					c0.4202881,0.2437134,0.6693115,0.5936737,0.7059326,0.9890289c0.0377808,0.4300232-0.1838379,0.8865509-0.6126099,1.2513123
					l-6.9539795,5.9243164c-0.5325317,0.4549408-1.3276978,0.7263947-2.1224365,0.7263947
					c-0.5220947,0-0.9949951-0.115387-1.3685913-0.3313446l-7.4644775-4.3265076
					c-0.418335-0.2424469-0.6696167-0.5936737-0.7045898-0.9877625c-0.0394287-0.4316101,0.1837769-0.888443,0.6131592-1.2525787
					L826.9683838,140.828125z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M814.8066406,133.7804871c0.5325317-0.4549408,1.3276367-0.7263947,2.1227417-0.7263947
					c0.5220947,0,0.9946899,0.1150818,1.367981,0.3313599l7.4647827,4.3265076
					c0.4126587,0.2392883,0.6592407,0.583252,0.6942139,0.9716644c0.0394287,0.4303589-0.1866455,0.8900299-0.619812,1.2585754
					l-6.9543457,5.9243164c-0.5378418,0.459671-1.3329468,0.7336426-2.1236572,0.7336426
					c-0.5117188,0-0.9786377-0.1141205-1.3481445-0.3278809l-7.4638062-4.3255615
					c-0.4190063-0.2433929-0.6699219-0.5936584-0.7049561-0.9890137c-0.0393677-0.4303436,0.1838379-0.888443,0.6126099-1.2528992
					L814.8066406,133.7804871z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M802.6452637,126.7318954c0.5366211-0.4565125,1.3273315-0.7295456,2.1164551-0.7295456
					c0.5192261,0,0.9949951,0.1153946,1.3745728,0.3357697l7.4638062,4.3265076
					c0.4130249,0.2392883,0.6595459,0.5835724,0.6945801,0.970108c0.0393677,0.4303436-0.1869507,0.8900146-0.6213989,1.2588806
					l-6.9524536,5.9255829c-0.5385132,0.4580994-1.3320313,0.7323761-2.1236572,0.7323761
					c-0.5123901,0-0.9790039-0.1125488-1.3481445-0.3269348l-7.4644165-4.326828
					c-0.4246826-0.2449646-0.6772461-0.5964966-0.7119141-0.9871216c-0.0397339-0.4275055,0.1866455-0.8859253,0.6185913-1.2532196
					L802.6452637,126.7318954z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M742.8068848,102.0090408l6.9523926-5.9258957
					c0.5365601-0.4565201,1.3276367-0.7292328,2.1164551-0.7292328c0.5195923,0,0.9949951,0.1150742,1.3745728,0.3354568
					l7.4644165,4.3268204c0.4127197,0.2389755,0.6589355,0.5835724,0.6939697,0.9701004
					c0.0393677,0.4300308-0.1863403,0.8897018-0.6195068,1.2585754l-6.9543457,5.9255753
					c-0.538147,0.4580917-1.3314209,0.7323837-2.1237183,0.7323837c-0.5122681,0-0.9788818-0.112236-1.3480835-0.3269424
					l-7.4644165-4.3265076c-0.4247437-0.2449646-0.6772461-0.5968094-0.7119141-0.9877548
					C742.1488647,102.8344269,742.3733521,102.3766479,742.8068848,102.0090408z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M761.9210205,103.1320419c0.5321655-0.4552536,1.3272705-0.7267075,2.1223755-0.7267075
					c0.5220947,0,0.9946899,0.1153946,1.367981,0.3313599l7.4644165,4.3265076
					c0.4130249,0.2392883,0.6599121,0.5838852,0.6945801,0.9713593c0.0394287,0.4306641-0.1866455,0.8903351-0.619812,1.259201
					l-6.9540405,5.9240036c-0.5384521,0.459671-1.333252,0.733963-2.1239624,0.733963
					c-0.5133057,0-0.9786377-0.1138153-1.3474731-0.3282013l-7.4644775-4.3252487
					c-0.4190063-0.2437057-0.6699219-0.5939789-0.7049561-0.9890137c-0.0393677-0.4306641,0.1837769-0.888443,0.6125488-1.2532196
					L761.9210205,103.1320419z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M787.4140625,113.3406219c-0.5236816,0-1.0019531-0.115387-1.3812256-0.335762
					l-7.4660034-4.326828c-0.4187012-0.2418137-0.6696167-0.5892487-0.7046509-0.975769
					c-0.0378418-0.428772,0.1793823-0.8679504,0.6141968-1.2384033l6.9523926-5.9255753
					c0.538208-0.4580917,1.3320313-0.7320709,2.1240234-0.7320709c0.5120239,0,0.9789429,0.1119232,1.3477783,0.3266296
					l7.4644165,4.3268204c0.4199829,0.2437057,0.6696777,0.5936661,0.706543,0.9890137
					c0.0375366,0.4300385-0.1837769,0.8868713-0.6132202,1.2516403l-6.9520874,5.9236908
					C788.9806519,113.0735855,788.1990967,113.3406219,787.4140625,113.3406219z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M786.2633057,117.239624c0.538147-0.459671,1.3317261-0.7336426,2.1224365-0.7336426
					c0.5132446,0,0.9801636,0.1134949,1.3493652,0.3281937l7.4644165,4.3252487
					c0.4243774,0.2462311,0.6784668,0.5965042,0.713501,0.9877548c0.0378418,0.4272003-0.1869507,0.8852921-0.6201782,1.2525864
					l-6.9523926,5.9255829c-0.5366211,0.4568329-1.3273315,0.729538-2.1167603,0.729538
					c-0.5195923,0-0.9946899-0.1150665-1.3739624-0.3357697l-7.4660034-4.3249283
					c-0.4130249-0.2392883-0.6592407-0.585144-0.6942749-0.9716721c-0.0393677-0.4300385,0.1866455-0.8897018,0.6210938-1.2585754
					L786.2633057,117.239624z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M839.1489868,147.888382c0.5385132-0.459671,1.3320313-0.733963,2.1223755-0.733963
					c0.5136108,0,0.9805298,0.1138153,1.3494263,0.3282013l7.4647217,4.3252411
					c0.4199829,0.2437134,0.6696777,0.5955505,0.7059326,0.9906006c0.038147,0.4303436-0.1850586,0.8856049-0.6126099,1.2503662
					l-6.9536743,5.9255829c-0.5327759,0.4549408-1.3247681,0.724823-2.1211548,0.724823
					c-0.520813,0-0.9949951-0.1138153-1.3682861-0.3310394l-7.4660034-4.3252563
					c-0.4130249-0.2396088-0.6592407-0.585144-0.6945801-0.9716644c-0.0390625-0.4303589,0.1869507-0.8900299,0.6213989-1.2588959
					L839.1489868,147.888382z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M859.574646,55.8289299c0.5151978,0,0.9859009,0.1150742,1.361084,0.3326111
					l7.4660034,4.3265114c0.4302979,0.2496948,0.6856689,0.6012268,0.7207031,0.9918518
					c0.0394287,0.4262505-0.1777954,0.8651123-0.6113281,1.2339821l-6.9527588,5.9243202
					c-0.5365601,0.4565125-1.3269653,0.7295456-2.1167603,0.7295456c-0.5189209,0-0.9943237-0.1153946-1.3735962-0.3357697
					l-7.4663696-4.3252487c-0.4123535-0.2405548-0.6592407-0.5848312-0.6942139-0.9713593
					c-0.0394287-0.4306641,0.1869507-0.8897018,0.6213989-1.2592049l6.9524536-5.923687
					C858.012146,56.1000633,858.7959595,55.8289299,859.574646,55.8289299z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M833.138916,42.4445915c0.5249023-0.4476891,1.3071289-0.7144127,2.090271-0.7144127
					c0.5255737,0,1.0038452,0.115078,1.3831177,0.335453l7.4644165,4.3265076
					c0.4202881,0.2437057,0.6696777,0.5905075,0.7046509,0.9770355c0.0394287,0.4275131-0.1777954,0.8682671-0.6126099,1.2371368
					l-6.9523926,5.9240036c-0.538208,0.4596672-1.333313,0.7339554-2.1240234,0.7339554
					c-0.5135498,0-0.9789429-0.1138115-1.3480835-0.3281975l-7.4656982-4.3252487
					c-0.4187012-0.2437057-0.6699829-0.5949211-0.7049561-0.9893303c-0.0394287-0.4300346,0.1837769-0.8865509,0.6132202-1.2513237
					L833.138916,42.4445915z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M875.9494019,65.3249893c0.5223389,0,1.0022583,0.1169662,1.3868408,0.3404922
					l7.4644775,4.3265152c0.4205322,0.2437057,0.6699219,0.5908203,0.7049561,0.9773483
					c0.0393677,0.4275131-0.1778564,0.8679504-0.6126099,1.2368164l-6.9542847,5.9255829
					c-0.5366211,0.4580917-1.3301392,0.7323837-2.1221313,0.7323837c-0.5120239,0-0.9789429-0.1122437-1.3481445-0.3269424
					l-7.4660034-4.3265076c-0.4243164-0.2449722-0.6765747-0.596817-0.7115479-0.9877548
					c-0.0378418-0.4271927,0.1865845-0.8852921,0.619812-1.253212l6.9524536-5.9236908
					C874.3925781,65.593605,875.1715698,65.3249893,875.9494019,65.3249893z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M847.411377,48.7819176c0.5164185,0,0.9877319,0.115078,1.3626099,0.3323021
					l7.4647827,4.3249283c0.4255981,0.2468605,0.6793823,0.6012306,0.7143555,0.9962692
					c0.0394287,0.4294014-0.1749268,0.8651123-0.6036987,1.2301979l-6.9524536,5.9236908
					c-0.5328369,0.4555702-1.3260498,0.7267036-2.1224365,0.7267036c-0.520813,0-0.9949951-0.115387-1.3682861-0.3310356
					l-7.4660034-4.3268242c-0.4130249-0.2396088-0.6592407-0.5835724-0.694519-0.9697838
					c-0.0391235-0.4306641,0.1868896-0.8903351,0.6213989-1.2592049l6.9523926-5.9255791
					C845.850769,49.0511627,846.6326294,48.7819176,847.411377,48.7819176z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M888.1151733,72.3786163c0.5255737,0,1.0038452,0.1150742,1.3831177,0.3351364
					l7.4644165,4.326828c0.4202881,0.2433929,0.6696777,0.5908203,0.7046509,0.9773483
					c0.0394287,0.4275131-0.1777954,0.8679504-0.612915,1.2371368l-6.9536743,5.9243164
					c-0.538147,0.4590378-1.3320313,0.7336426-2.1224365,0.7336426c-0.5135498,0-0.979248-0.1138153-1.3480835-0.3285141
					l-7.4656982-4.3252487c-0.4187012-0.2433929-0.6699829-0.5949173-0.7049561-0.9890137
					c-0.0394287-0.4303513,0.1837769-0.8865509,0.6125488-1.2516403l6.9527588-5.9252625
					C886.5498657,72.64534,887.3320313,72.3786163,888.1151733,72.3786163z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M912.4606323,86.4773712c0.5151978,0,0.9859009,0.1150742,1.361084,0.3326111
					l7.4644165,4.3265076c0.4259033,0.2468567,0.6793823,0.5996552,0.7150269,0.9950027
					c0.0387573,0.4287796-0.175293,0.8647995-0.6040649,1.2295761l-6.9527588,5.9255753
					c-0.5321655,0.4536819-1.3256836,0.7251282-2.1211548,0.7251282c-0.5220947,0-0.9962769-0.1138077-1.3695679-0.3313522
					l-7.4660034-4.3252411c-0.4130249-0.2389832-0.6592407-0.5848389-0.6942139-0.9713669
					c-0.0394287-0.4303436,0.1866455-0.8897018,0.6213989-1.2588882l6.9524536-5.9240036
					C910.8981323,86.7488174,911.6819458,86.4773712,912.4606323,86.4773712z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M900.2784424,79.4256287c0.5236816,0,1.0022583,0.1166534,1.3815308,0.3370285
					l7.4647217,4.3252487c0.4256592,0.2465439,0.6794434,0.600914,0.7144775,0.996582
					c0.0393677,0.428772-0.1749878,0.8647919-0.6037598,1.2295685L902.282959,92.23806
					c-0.5325317,0.4552536-1.3260498,0.726387-2.1224365,0.726387c-0.520813,0-0.9949951-0.1150742-1.3682861-0.3313522
					l-7.4660034-4.3265076c-0.4187012-0.2421265-0.6696167-0.5936584-0.7046509-0.9877548
					c-0.0393677-0.4319229,0.1838379-0.8881226,0.6126099-1.2528992l6.9520874-5.9240036
					C898.7131348,79.6942444,899.4949951,79.4256287,900.2784424,79.4256287z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M786.5609131,96.6361313c-0.5167236,0-0.9877319-0.115387-1.3623047-0.3326111
					l-7.4663086-4.3252487c-0.4243774-0.2465439-0.6784668-0.5993347-0.7147217-0.9946899
					c-0.038147-0.4290848,0.1765747-0.8666916,0.6053467-1.2311478l6.9526978-5.9255753
					c0.5366211-0.4565201,1.3257446-0.7292328,2.1133423-0.7292328c0.5223389,0,0.9993896,0.1150742,1.3773804,0.3351364
					l7.4641113,4.3255615c0.4190063,0.2433929,0.668396,0.5892487,0.7034302,0.9745178
					c0.038147,0.4312897-0.1851196,0.8777237-0.6289673,1.2557373l-6.9537354,5.9255753
					C788.1218262,96.3662567,787.3396606,96.6361313,786.5609131,96.6361313z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M798.7427979,103.687561c-0.5249023,0-1.003479-0.1150818-1.3831177-0.3351364
					l-7.4656982-4.326828c-0.4190063-0.2421265-0.6696167-0.5892487-0.7045898-0.975769
					c-0.0375366-0.4290924,0.1796875-0.8679504,0.6144409-1.2387161l6.9521484-5.9252625
					c0.5413208-0.4612503,1.3320313-0.7368011,2.116394-0.7368011c0.5126343,0,0.9821167,0.1153946,1.3554077,0.3310394
					l7.4660034,4.3265076c0.4233398,0.2456055,0.6768799,0.596817,0.711853,0.9909134
					c0.0393677,0.4316101-0.1806641,0.8767776-0.619812,1.250061l-6.9527588,5.9255753
					C800.3081665,103.4208374,799.5262451,103.687561,798.7427979,103.687561z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M810.9089355,110.7408752c-0.5223999,0-1.0019531-0.1182327-1.387207-0.3414459
					l-7.4644165-4.3252487c-0.4202881-0.2437057-0.6712646-0.5905075-0.7062378-0.9770279
					c-0.038147-0.4275131,0.1793823-0.8682709,0.6141357-1.2387161l6.9527588-5.9236908
					c0.5410156-0.4612427,1.3314209-0.7371063,2.1148682-0.7371063c0.5135498,0,0.9820557,0.1138153,1.3569336,0.3313522
					l7.4656982,4.3265076c0.4290771,0.2481232,0.6828613,0.5980759,0.7191162,0.9877548
					c0.0394287,0.4303513-0.1838379,0.8751984-0.6273804,1.253212l-6.9520874,5.9236908
					C812.4654541,110.4713135,811.6848145,110.7408752,810.9089355,110.7408752z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M810.4669189,61.7642822c0.5366211-0.4562035,1.3260498-0.7289162,2.1135864-0.7289162
					c0.5223999,0,0.9994507,0.115078,1.3771362,0.3351364l7.4647217,4.3252449
					c0.4187012,0.2424469,0.6680908,0.5892487,0.703064,0.9745178c0.0375366,0.4316101-0.1856689,0.8780365-0.6286621,1.2560501
					l-6.9539795,5.9240036c-0.53125,0.4521027-1.3128052,0.7232361-2.0934448,0.7232361
					c-0.5132446,0-0.9845581-0.1150742-1.359436-0.3326111l-7.4660034-4.3265076
					c-0.4246826-0.2462311-0.6781616-0.5993347-0.7144165-0.9931183c-0.038147-0.428772,0.1762085-0.866684,0.6049805-1.2314606
					L810.4669189,61.7642822z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M821.8117676,52.0973434c0.5252075-0.4480057,1.3071289-0.7150421,2.0905762-0.7150421
					c0.5249023,0,1.003479,0.1153908,1.3831177,0.3357658l7.4644165,4.3265076
					c0.4199219,0.2437096,0.6690063,0.5911407,0.7039795,0.9776649c0.0394287,0.4275131-0.1778564,0.8676376-0.6125488,1.236824
					l-6.9536743,5.9240036c-0.5410156,0.4625015-1.3317261,0.7380524-2.1151733,0.7380524
					c-0.5123291,0-0.9805298-0.1150742-1.3551025-0.3326111l-7.4660034-4.3249321
					c-0.4234009-0.2465439-0.6768799-0.5983925-0.711853-0.990593c-0.0394287-0.4331856,0.1806641-0.8783531,0.619812-1.2519531
					L821.8117676,52.0973434z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M795.920166,74.1596069c0.5249023-0.4480057,1.3071289-0.7150421,2.090271-0.7150421
					c0.5255127,0,1.0038452,0.1156998,1.3830566,0.3354492l7.4660645,4.3271408
					c0.4187012,0.2418137,0.6693115,0.5889282,0.7042847,0.975769c0.038147,0.4290924-0.1790771,0.8676376-0.6138306,1.2383957
					l-6.9523926,5.924324c-0.538208,0.4593506-1.333313,0.7333221-2.1240234,0.7333221
					c-0.5123291,0-0.9789429-0.1138077-1.3480835-0.3278809l-7.4660034-4.3252487
					c-0.418396-0.2437057-0.6696777-0.5952377-0.7046509-0.9890137c-0.0394287-0.4303513,0.1837769-0.8871841,0.6132202-1.2516403
					L795.920166,74.1596069z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M840.3013306,143.9887543c-0.5249634,0-1.00354-0.1163483-1.3828125-0.3370361
					l-7.4660034-4.3249359c-0.4240723-0.2465363-0.6781616-0.5996399-0.7147217-0.9946899
					c-0.0378418-0.4290771,0.1768188-0.866684,0.6055908-1.2314606l6.9524536-5.9243164
					c0.5321655-0.4549408,1.3273315-0.7263947,2.1224365-0.7263947c0.5223999,0,0.9949951,0.1157074,1.3682861,0.3313599
					l7.4644165,4.3265076c0.4199829,0.2437134,0.6696777,0.5939789,0.7059326,0.9893341
					c0.0394287,0.4303436-0.1838379,0.8871765-0.6126099,1.2516327l-6.9539795,5.9240112
					C841.8654175,143.7223358,841.085083,143.9887543,840.3013306,143.9887543z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M799.5817871,120.3936234c-0.5224609,0-1.0022583-0.1179123-1.3875732-0.3411255
					l-7.4656982-4.3268204c-0.4189453-0.2421341-0.6696167-0.5895691-0.7045898-0.9760895
					c-0.0378418-0.4290924,0.1793213-0.8676376,0.6141357-1.2384033l6.9524536-5.9243164
					c0.538147-0.4590378,1.333252-0.7336426,2.1236572-0.7336426c0.5123291,0,0.979248,0.1138153,1.3481445,0.3285141
					l7.4656372,4.3249359c0.4249878,0.245285,0.6768799,0.5968094,0.7119141,0.9877548
					c0.038147,0.4271927-0.1863403,0.8856049-0.6195068,1.253212l-6.9524536,5.9252625
					C801.1379395,120.1237488,800.357666,120.3936234,799.5817871,120.3936234z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M815.9561768,129.8884277c-0.5148926,0-0.9862061-0.115387-1.361084-0.3326111
					l-7.4656982-4.3249359c-0.4302979-0.249382-0.6856689-0.6024933-0.7219238-0.9934311
					c-0.038147-0.4262543,0.1790771-0.8635406,0.6109619-1.2327271l6.9536743-5.923996
					c0.5369263-0.4580994,1.3276367-0.7308121,2.1167603-0.7308121c0.520874,0,0.9949951,0.1150742,1.3743286,0.3354568
					l7.4643555,4.3265076c0.4130249,0.2396088,0.6593018,0.5835724,0.6942749,0.9701004
					c0.0394287,0.4303436-0.1863403,0.8903351-0.619812,1.259201l-6.9540405,5.9255829
					C817.5170898,129.618866,816.7352295,129.8884277,815.9561768,129.8884277z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M828.1165771,136.9373322c-0.5132446,0-0.9845581-0.1150818-1.359436-0.3326263
					l-7.4660034-4.3268127c-0.4246826-0.2465515-0.6781616-0.5996552-0.7150269-0.9934387
					c-0.0375366-0.428772,0.1765137-0.8660583,0.6055908-1.2308197l6.9524536-5.9255829
					c0.5321655-0.4536819,1.3257446-0.7251358,2.1211548-0.7251358c0.5220947,0,0.9962769,0.1138153,1.3695679,0.3313522
					l7.4647217,4.3252563c0.4127197,0.2389679,0.6592407,0.5848236,0.6942749,0.9713593
					c0.0393677,0.4300232-0.1869507,0.8896942-0.619873,1.259201l-6.9539795,5.9236908
					C829.678772,136.6661835,828.8968506,136.9373322,828.1165771,136.9373322z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M852.4633179,151.0360718c-0.5249023,0-1.0038452-0.1150665-1.3831177-0.3354492
					l-7.4656372-4.3265076c-0.4190063-0.2424469-0.6696777-0.5892487-0.7046509-0.975769
					c-0.038147-0.428772,0.1793823-0.8682709,0.6141357-1.2387238l6.9524536-5.923996
					c0.538147-0.4593506,1.333252-0.7336426,2.1239624-0.7336426c0.5120239,0,0.9789429,0.1138153,1.3478394,0.3282013
					l7.4644165,4.3252411c0.4202271,0.2433929,0.6696167,0.5949249,0.7061768,0.9906006
					c0.0378418,0.4300232-0.1853638,0.8852844-0.6125488,1.250061l-6.9543457,5.9255676
					C854.0270996,150.7693481,853.2467651,151.0360718,852.4633179,151.0360718z"
              />
              <path
                opacity="0.7"
                fill="#47A7D5"
                d="M827.2820435,120.2375641c-0.5135498,0-0.9845581-0.115387-1.359436-0.3326111
					l-7.4660034-4.326828c-0.4303589-0.249382-0.68573-0.6024857-0.7207031-0.9934311
					c-0.0391235-0.425621,0.1781006-0.863533,0.6112671-1.2324066l6.9524536-5.9240036
					c0.5397339-0.4612427,1.3292236-0.7355347,2.1094971-0.7355347c0.5192871,0,0.9975586,0.1185455,1.3815308,0.3401871
					l7.4644165,4.3268204c0.418396,0.2418137,0.6680908,0.5873566,0.703064,0.9726181
					c0.038147,0.4319305-0.1837769,0.8783569-0.6286621,1.2557373l-6.9539795,5.9255829
					C828.8445435,119.9661102,828.0623779,120.2375641,827.2820435,120.2375641z"
              />
            </g>
          </g>
          <g id="Axonometric_Cylinder_2_">
            <path
              id="Cylinder_face_102_"
              fill="#DBDBDB"
              d="M878.8152466,201.4433746c0,1.2254791,0.8603516,1.7223511,1.921875,1.1094513
				c1.0615234-0.6128845,1.9222412-2.1031952,1.9222412-3.3289795s-0.8607178-1.7223358-1.9222412-1.1094513
				C879.6755981,198.7269745,878.8152466,200.2175903,878.8152466,201.4433746z"
            />
          </g>
          <g id="Axonometric_Cylinder_5_">
            <path
              id="Cylinder_face_104_"
              fill="#DBDBDB"
              d="M814.434021,164.2725525c0,1.2257843,0.8603516,1.7226563,1.921875,1.1097565
				c1.0615845-0.6128845,1.921936-2.1031952,1.921936-3.3289795s-0.8603516-1.7223358-1.921936-1.1097565
				C815.2943726,161.5564575,814.434021,163.0470734,814.434021,164.2725525z"
            />
          </g>
        </g>
        <g id="central-unit-node">
          <g enable-background="new    ">

            <linearGradient
              id="SVGID_22_"
              gradientUnits="userSpaceOnUse"
              x1="1116.2246094"
              y1="184.605423"
              x2="1187.1563721"
              y2="184.605423"
            >
              <stop
                offset="0"
                style="stop-color:#D3CFC9"
              />
              <stop
                offset="1"
                style="stop-color:#E5E1DA"
              />
            </linearGradient>
            <path
              fill="url(#SVGID_22_)"
              d="M1116.2557373,128.6457825l-0.0310059,91.3389893
				c-0.0072021,2.5457458,1.6721191,5.0955963,5.0372314,7.0385132l18.44104,10.6469269
				c6.6890869,3.8618164,17.4970703,3.8578796,24.1358643-0.0001678l18.3278809-10.6510468
				c3.2989502-1.9171906,4.9515381-4.4274445,4.9586182-6.9418182l0.0310059-91.3391571L1116.2557373,128.6457825z"
            />
            <g>
              <path
                opacity="0.8"
                fill="#EEEAE3"
                d="M1182.1977539,135.6800079l-0.0310059,91.3389893
					c3.2989502-1.9171906,4.9515381-4.4274445,4.9586182-6.9418182l0.0310059-91.3391571
					C1187.1491699,131.2525635,1185.496582,133.7628174,1182.1977539,135.6800079z"
              />
              <path
                opacity="0.3"
                fill="#C1BEB9"
                d="M1116.2557373,128.6457825l-0.0310059,91.3389893
					c-0.0072021,2.5457458,1.6721191,5.0955963,5.0372314,7.0385132l0.0311279-91.3391724
					C1117.9278564,133.7412109,1116.2485352,131.1913452,1116.2557373,128.6457825z"
              />

              <linearGradient
                id="SVGID_23_"
                gradientUnits="userSpaceOnUse"
                x1="1139.7030029"
                y1="193.4480591"
                x2="1163.869751"
                y2="193.4480591"
              >
                <stop
                  offset="0"
                  style="stop-color:#E0DCD4"
                />
                <stop
                  offset="1"
                  style="stop-color:#E5E1DA"
                />
              </linearGradient>
              <path
                opacity="0.8"
                fill="url(#SVGID_23_)"
                d="M1139.7340088,146.3312225l-0.0310059,91.3389893
					c6.6890869,3.8618164,17.4970703,3.8578796,24.1358643-0.0001678l0.0308838-91.3390045
					C1157.2310791,150.1891022,1146.4230957,150.1930389,1139.7340088,146.3312225z"
              />
            </g>
            <path
              fill="#F7F3EC"
              d="M1182.1191406,121.6996765l-18.44104-10.6469269c-6.6890869-3.8619919-17.4970703-3.8580475-24.1358643,0
				l-18.3278809,10.6510468c-6.6386719,3.8580475-6.6104736,10.1184998,0.0787354,13.9803162l18.440918,10.64711
				c6.6890869,3.8618164,17.4970703,3.8578796,24.1357422-0.0001831l18.3280029-10.6510315
				C1188.8364258,131.8219604,1188.8082275,125.5615005,1182.1191406,121.6996765z"
            />

            <linearGradient
              id="SVGID_24_"
              gradientUnits="userSpaceOnUse"
              x1="1117.6816406"
              y1="128.6921539"
              x2="1185.7313232"
              y2="128.6921539"
            >
              <stop
                offset="0"
                style="stop-color:#E5E1DA"
              />
              <stop
                offset="1"
                style="stop-color:#D3CFC9"
              />
            </linearGradient>
            <path
              fill="url(#SVGID_24_)"
              d="M1151.8170166,147.8007507c-4.3127441-0.0006866-8.3498535-0.9606628-11.3695068-2.7043457
				l-18.4417725-10.6470947c-2.7857666-1.6078949-4.3212891-3.6605377-4.3240967-5.780899
				c-0.0026855-2.1020203,1.5064697-4.138031,4.2502441-5.7320404l18.3276367-10.6506958
				c2.9971924-1.7429962,7.0234375-2.7021103,11.3348389-2.7021103c4.3128662,0,8.3514404,0.9598007,11.3710938,2.7041702
				l18.4404297,10.6464081c2.7855225,1.6080627,4.3225098,3.6613922,4.3254395,5.7815857
				c0.0026855,2.1021881-1.5064697,4.1375275-4.2503662,5.7315216l-18.3275146,10.6506958
				C1160.1560059,146.8407745,1156.1286621,147.8007507,1151.8170166,147.8007507L1151.8170166,147.8007507z"
            />
            <path
              fill="#F7F3EC"
              d="M1151.8170166,147.9999695c-4.2640381-0.0006866-8.2510986-0.9472809-11.2275391-2.6659241
				l-18.4417725-10.6470947c-2.6938477-1.5550842-4.1778564-3.5201263-4.180542-5.5345306
				c-0.0029297-1.9962311,1.4561768-3.9446335,4.1081543-5.4849701l18.3276367-10.6506958
				c2.9541016-1.7171097,6.9287109-2.6631927,11.1914063-2.6631927c4.2641602,0,8.2526855,0.9467697,11.2290039,2.6659317
				l18.4405518,10.6465836c2.6937256,1.5549164,4.1791992,3.5206299,4.1818848,5.5350342
				c0.0028076,1.9957275-1.4561768,3.9439545-4.1081543,5.4844666l-18.3277588,10.650528
				C1160.0557861,147.0540619,1156.0799561,147.9999695,1151.8170166,147.9999695L1151.8170166,147.9999695z"
            />
          </g>
          <g>
            <polygon
              fill="#E3DFD8"
              points="1135.8609619,229.9436493 1125.2947998,223.843338 1125.2947998,206.3710175
				1135.8609619,212.4713287 			"
            />
            <polygon
              fill="#9DB4B0"
              points="1135.8609619,214.5351257 1125.2947998,208.4347992 1125.2947998,202.2506409
				1135.8609619,208.3509521 			"
            />
            <g opacity="0.5">
              <path
                fill="#3A313B"
                d="M1128.4558105,217.2072296l-2.1650391-1.2500763v-0.43927l2.1650391,1.2500763V217.2072296z
					 M1130.2901611,217.8268585l-1.3973389-0.8066864v0.4394379l1.3973389,0.8066864V217.8268585z M1130.7272949,218.0792389
					v0.4392548l1.3671875,0.7893829v-0.43927L1130.7272949,218.0792389z M1133.5026855,219.6816406l-0.9711914-0.5606537v0.43927
					l0.9711914,0.5608215V219.6816406z M1134.4886475,220.2508698l-0.5488281-0.3168488v0.43927l0.5488281,0.3168335V220.2508698z
					 M1127.5220947,217.4215393l-1.2020264-0.694046v0.43927l1.2020264,0.694046V217.4215393z M1129.2945557,218.4449463
					l-1.3354492-0.7711945v0.4394379l1.3354492,0.7710266V218.4449463z M1129.7315674,218.6971588v0.4392548l1.1851807,0.6842804
					v-0.43927L1129.7315674,218.6971588z M1132.798584,220.467926l-1.4448242-0.8341217v0.43927l1.4448242,0.8341217V220.467926z
					 M1134.4886475,221.4436646l-1.2530518-0.723526v0.4394379l1.2530518,0.7233582V221.4436646z M1126.62146,218.0943298
					l-0.3306885-0.1910095v0.43927l0.3306885,0.1909943V218.0943298z M1130.2901611,220.2122955l-3.2314453-1.8657684v0.4394379
					l3.2314453,1.8657684V220.2122955z M1130.7272949,220.4646759v0.4392548l1.3671875,0.7893829v-0.43927L1130.7272949,220.4646759
					z M1133.5026855,222.0670776l-0.9711914-0.5606537v0.43927l0.9711914,0.5608215V222.0670776z M1134.4886475,222.6363068
					l-0.5488281-0.3168488v0.4392548l0.5488281,0.3168488V222.6363068z M1128.4558105,212.251358l-2.1650391-1.2500763v-0.4392548
					l2.1650391,1.250061V212.251358z M1130.2901611,212.8709869l-1.3973389-0.8066864v0.43927l1.3973389,0.8068542V212.8709869z
					 M1130.7272949,213.1233673v0.43927l1.3671875,0.7893677v-0.43927L1130.7272949,213.1233673z M1133.5026855,214.725769
					l-0.9711914-0.5606537v0.43927l0.9711914,0.5608215V214.725769z M1134.4886475,215.2949982l-0.5488281-0.3168488v0.43927
					l0.5488281,0.3168488V215.2949982z M1127.5220947,212.4656677l-1.2020264-0.6940308v0.4392548l1.2020264,0.694046V212.4656677z
					 M1129.2945557,213.4889069l-1.3354492-0.7710266v0.43927l1.3354492,0.7711945V213.4889069z M1129.7315674,213.7412872v0.43927
					l1.1851807,0.6842651v-0.43927L1129.7315674,213.7412872z M1132.798584,215.5120544l-1.4448242-0.8341217v0.43927
					l1.4448242,0.8341217V215.5120544z M1134.4886475,216.4878082l-1.2530518-0.7235413v0.4394379l1.2530518,0.7233582V216.4878082z
					 M1126.62146,214.5490112l-0.3306885-0.1910095v0.43927l0.3306885,0.1909943V214.5490112z M1130.2901611,216.6669769
					l-3.2314453-1.8655853v0.4392548l3.2314453,1.8657684V216.6669769z M1130.7272949,216.9193573v0.4392548l1.3671875,0.7893829
					v-0.43927L1130.7272949,216.9193573z M1133.5026855,218.521759l-0.9711914-0.5606537v0.43927l0.9711914,0.5608215V218.521759z
					 M1134.4886475,219.0909882l-0.5488281-0.3168488v0.43927l0.5488281,0.3168335V219.0909882z M1128.4558105,221.4313202
					l-2.1650391-1.2498932v0.43927l2.1650391,1.2498932V221.4313202z M1130.2901611,222.4904022l-1.3973389-0.8067017v0.43927
					l1.3973389,0.8066864V222.4904022z M1130.7272949,222.7427673v0.43927l1.3671875,0.7893677v-0.4392548L1130.7272949,222.7427673
					z M1133.5026855,224.3451843l-0.9711914-0.5608368v0.4394379l0.9711914,0.5606537V224.3451843z M1134.4886475,224.9143982
					l-0.5488281-0.3168335v0.4392548l0.5488281,0.3168488V224.9143982z M1127.5220947,222.0848999l-1.2020264-0.6938629v0.4392548
					l1.2020264,0.6938782V222.0848999z M1129.2945557,223.1083069l-1.3354492-0.7710266v0.43927l1.3354492,0.7710266V223.1083069z
					 M1129.7315674,223.3606873v0.43927l1.1851807,0.6842651v-0.4394226L1129.7315674,223.3606873z"
              />
            </g>
          </g>
          <g>
            <path
              opacity="0.6"
              fill="#D3CFC9"
              d="M1169.0966797,124.5984421l-10.5488281-6.0905457
				c-2.2172852-1.2800751-5.2218018-1.7935715-8.0987549-1.5900574c-2.8814697-0.2040329-5.8817139,0.3105011-8.0837402,1.5902328
				l-10.4842529,6.0927734c-3.7976074,2.2069473-3.7813721,5.7881012,0.0449219,7.9971161l10.5490723,6.0905457
				c2.2171631,1.2800598,5.2218018,1.7935638,8.0987549,1.5900574c2.8813477,0.2040253,5.8817139-0.3106689,8.0837402-1.5902405
				l10.4841309-6.0926056C1172.9393311,130.3887787,1172.9229736,126.8074493,1169.0966797,124.5984421z"
            />
            <g enable-background="new    ">

              <linearGradient
                id="SVGID_25_"
                gradientUnits="userSpaceOnUse"
                x1="1131.4029541"
                y1="132.679657"
                x2="1171.9782715"
                y2="132.679657"
              >
                <stop
                  offset="0"
                  style="stop-color:#D3CFC9"
                />
                <stop
                  offset="1"
                  style="stop-color:#E5E1DA"
                />
              </linearGradient>
              <path
                fill="url(#SVGID_25_)"
                d="M1131.4206543,125.0632553l-0.0177002,3.4601059
					c-0.0042725,1.4563293,0.9564209,2.9148865,2.8814697,4.0262451l10.5488281,6.0905457
					c3.8262939,2.2090149,10.0089111,2.2067719,13.8065186-0.0001678l10.484375-6.0927734
					c1.8869629-1.0966187,2.8323975-2.532547,2.8363037-3.970871l0.0178223-3.4602737L1131.4206543,125.0632553z"
              />
              <g>
                <path
                  opacity="0.8"
                  fill="#EEEAE3"
                  d="M1169.1417236,129.0870972l-0.0175781,3.4601135
						c1.8869629-1.0966187,2.8323975-2.532547,2.8363037-3.970871l0.0178223-3.4602737
						C1171.9742432,126.5545578,1171.0288086,127.9904785,1169.1417236,129.0870972z"
                />
                <path
                  opacity="0.3"
                  fill="#C1BEB9"
                  d="M1131.4206543,125.0632553l-0.0177002,3.4601059
						c-0.0042725,1.4563293,0.9564209,2.9148865,2.8814697,4.0262451l0.0175781-3.4601135
						C1132.3771973,127.9781342,1131.4165039,126.5195847,1131.4206543,125.0632553z"
                />

                <linearGradient
                  id="SVGID_26_"
                  gradientUnits="userSpaceOnUse"
                  x1="1144.833252"
                  y1="137.7379608"
                  x2="1158.6575928"
                  y2="137.7379608"
                >
                  <stop
                    offset="0"
                    style="stop-color:#E0DCD4"
                  />
                  <stop
                    offset="1"
                    style="stop-color:#E5E1DA"
                  />
                </linearGradient>
                <path
                  opacity="0.8"
                  fill="url(#SVGID_26_)"
                  d="M1144.8509521,135.1800385l-0.0177002,3.4601135
						c3.8262939,2.2090149,10.0089111,2.2067719,13.8065186-0.0001678l0.0178223-3.4601135
						C1154.8598633,137.3868256,1148.6773682,137.3890533,1144.8509521,135.1800385z"
                />
              </g>
              <path
                fill="#F7F3EC"
                d="M1169.0966797,121.0899887l-10.5488281-6.0905457
					c-3.8262939-2.2091827-10.0089111-2.2067795-13.8065186,0l-10.4842529,6.0927734
					c-3.7976074,2.206955-3.7813721,5.7882767,0.0449219,7.9972763l10.5489502,6.0905457
					c3.826416,2.2090149,10.0089111,2.2067871,13.8066406-0.0001678l10.4841309-6.0927734
					C1172.9393311,126.8801498,1172.9232178,123.298996,1169.0966797,121.0899887z"
              />
            </g>
          </g>
          <path
            fill="#3A313B"
            d="M1176.7838135,183.8656616l-6.9958496,4.0389404
			c-0.6612549,0.3818207-1.1972656,0.1553345-1.1972656-0.5059662v-24.532608c0-0.6612854,0.5360107-1.5067291,1.1972656-1.8885651
			l6.9958496-4.039093c0.6612549-0.3816528,1.1972656-0.1551666,1.1972656,0.506134v24.5324249
			C1177.9810791,182.6382294,1177.4450684,183.4838409,1176.7838135,183.8656616z"
          />
          <g>
            <path
              fill="#FFFFFF"
              d="M1174.7485352,163.6005402c0,0.807724-0.6547852,1.8405457-1.4625244,2.3069
				c-0.8078613,0.4665375-1.4626465,0.1896362-1.4626465-0.6180878c0-0.8077087,0.6547852-1.8407288,1.4626465-2.3070679
				C1174.09375,162.5159302,1174.7485352,162.7926483,1174.7485352,163.6005402z"
            />
          </g>
        </g>
      </g>
      <g id="dynamic-inputs">
        <path
          id="dynamic-val-panel"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          d="M971.670105,103.9843979h74.3651733
		c3.2213135,0,5.8327637,3.6517029,5.8327637,8.156311v7.6220322c0,4.5046082-2.6114502,8.156311-5.8327637,8.156311H971.670105
		c-3.2213745,0-5.8327637-3.6517029-5.8327637-8.156311v-7.6220322
		C965.8373413,107.6361008,968.4487305,103.9843979,971.670105,103.9843979z"
        />
        <path
          id="dynamic-val-plant"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          d="M722.8641357,330.529541h74.3651123
		c3.2213745,0,5.8327637,3.6517029,5.8327637,8.156311v7.6220398c0,4.5046082-2.6113892,8.156311-5.8327637,8.156311h-74.3651123
		c-3.2213745,0-5.8328247-3.6517029-5.8328247-8.156311v-7.6220398
		C717.031311,334.1812439,719.6427612,330.529541,722.8641357,330.529541z"
        />
        <path
          id="dynamic-val-house"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          d="M1266.675293,106.2247314h74.3651123
		c3.2214355,0,5.8327637,3.6517105,5.8327637,8.1563187v7.6220322c0,4.5046082-2.6113281,8.156311-5.8327637,8.156311h-74.3651123
		c-3.2213135,0-5.8327637-3.6517029-5.8327637-8.156311v-7.6220322
		C1260.8425293,109.876442,1263.4539795,106.2247314,1266.675293,106.2247314z"
        />
        <path
          id="dynamic-val-battery"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          d="M1187.6505127,296.5345764h74.3651123
		c3.2214355,0,5.8327637,3.6517334,5.8327637,8.1563416v7.6220093c0,4.5046082-2.6113281,8.156311-5.8327637,8.156311h-74.3651123
		c-3.2213135,0-5.8327637-3.6517029-5.8327637-8.156311v-7.6220093
		C1181.817749,300.1863098,1184.4291992,296.5345764,1187.6505127,296.5345764z"
        />
        <path
          id="dynamic-val-battery-percentage"
          fill-rule="evenodd"
          clip-rule="evenodd"
          fill="#FFFFFF"
          d="M1456.508667,350.9565125
		h-43.9620361c-4.4560547,0-8.0683594-3.6123352-8.0683594-8.0683899v-11.3478394
		c0-4.4560547,3.6123047-8.0683899,8.0683594-8.0683899h43.9620361c4.4560547,0,8.0684814,3.6123352,8.0684814,8.0683899v11.3478394
		C1464.5771484,347.3441772,1460.9647217,350.9565125,1456.508667,350.9565125z"
        />
      </g>
    </svg>
    <!-- eslint-enable -->
  </div>
</template>

<style lang="css">
#solar-scheme-container {
  #lines {
    path {
      stroke-width: 2px;
      stroke: rgba(0, 0, 0, 0.5);
    }
  }
  #dynamic-inputs {
    rect, path {
      fill: rgb(var(--prim-col-foreground-contrast));
      stroke: none;
    }
  }
}

.dark {
  #solar-scheme-01 {
    > text {
      fill: rgb(var(--prim-col-foreground-contrast));
    }
    #lines {
      path {
        stroke: rgba(255, 255, 255, 0.65);
      }
    }
    #dynamic-inputs {
      rect, path {
        fill: rgb(var(--prim-col-foreground-2));
        stroke: none;
      }
    }
  }
}

#solar-scheme-01 {
  #lines {
    #battery-central {
      stroke-width: v-bind('computedStrokeWidths.battery');
      opacity: v-bind('computedOpacity.battery')
    }
    #house-central {
      stroke-width: v-bind('computedStrokeWidths.house');
      opacity: v-bind('computedOpacity.house')
    }
    #plant-central {
      stroke-width: v-bind('computedStrokeWidths.plant');
      opacity: v-bind('computedOpacity.plant')
    }
    #solar-central {
      stroke-width: v-bind('computedStrokeWidths.solar');
      opacity: v-bind('computedOpacity.solar')
    }
    path {
      stroke-width: 0.015rem;
    }
  }
  #nodes {
    #battery-node {
      opacity: v-bind('computedOpacity.battery')
    }
    #solar-panel-node {
      opacity: v-bind('computedOpacity.solar')
    }
    #house-node {
      opacity: v-bind('computedOpacity.house')
    }
    #plant-node {
      opacity: v-bind('computedOpacity.plant')
    }
  }
}
</style>
